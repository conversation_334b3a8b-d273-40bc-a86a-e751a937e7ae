require 'rails_helper'

RSpec.describe Search::InternalLinkBodyType do
  describe '#call' do
    let(:country_code) { 1 }
    let(:bsty) { '6' }
    let(:current_user_id) { 123 }
    let(:solr_group_service) { instance_double(Solr::GroupService) }
    let(:search_condition_filter_service) { class_double(Search::SearchConditionFilter) }

    subject { described_class.new(country_code, bsty, current_user_id) }

    before do
      allow(TPopularRanking).to receive(:fetch_popular_ranking).and_return(
        double(select: [
                 instance_double(TPopularRanking, make_id: 1, model_id: 15),
                 instance_double(TPopularRanking, make_id: 2, model_id: 25),
               ]),
      )

      allow(MPrimaryBodyStyle).to receive(:find_by).and_return(double(name: 'Sedan'))

      allow(Search::SearchConditionFilter).to receive(:new)
        .and_return(double(query_data: 'make_id:1 model_id:15'))

      allow(Solr::GroupService).to receive(:new).and_return(solr_group_service)
      allow(solr_group_service).to receive(:group_query).and_return(
        {
          'grouped' => {
            'make_model' => {
              'doclist' => {
                'numFound' => 1,
                'docs' => [
                  {
                    'Make' => 'Toyota',
                    'Model' => 'Camry',
                    'BodyStyle1' => 'SEDAN',
                    'Price' => 5000,
                    'UserID' => 999
                  },
                ]
              }
            }
          }
        },
      )

      allow(MasterInfo::InternalLink::PriceRange).to receive(:all).and_return([
                                                                                double(:value => '[0 TO 500]', :text => 'Under US$500',
                                                                                       :query => 'prct=500', :id => 1),
                                                                                double(:value => '[500 TO 1000]', :text => 'US$500-1,000',
                                                                                       :query => 'prcf=500&prct=1000', :id => 2),
                                                                              ])
      allow(MasterInfo::InternalLink::OtherCategory).to receive(:all).and_return([
                                                                                   double(:text => 'Left Hand Drive',
                                                                                          :url => 'used_car/all/all/?st=13',
                                                                                          :query => 'st=13',
                                                                                          :solr_query => 'SteeringID:13',
                                                                                          :id => 1),
                                                                                   double(:text => 'Manual',
                                                                                          :url => 'used_car/all/all/?tmns=6',
                                                                                          :query => 'tmns=6',
                                                                                          :solr_query => 'TransmissionID:6',
                                                                                          :id => 2),
                                                                                 ])
      allow_any_instance_of(Solr::FacetService).to receive(:call_facet).with(
        queries: [
          'Price:[0 TO 500] AND BodyStyle1:6',
          'Price:[500 TO 1000] AND BodyStyle1:6',
          'MakeID:1 AND BodyStyle1:6',
          'MakeID:2 AND BodyStyle1:6',
          'MakeID:3 AND BodyStyle1:6',
          'MakeID:5 AND BodyStyle1:6',
          'MakeID:4 AND BodyStyle1:6',
          'SteeringID:13 AND BodyStyle1:6',
          'TransmissionID:6 AND BodyStyle1:6',
        ],
      ).and_return({
                     'facet_counts' => {
                       'facet_queries' => {
                         'Price:[0 TO 500] AND BodyStyle1:6' => 8,
                         'Price:[500 TO 1000] AND BodyStyle1:6' => 7,
                         'MakeID:1 AND BodyStyle1:6' => 12,
                         'MakeID:2 AND BodyStyle1:6' => 8,
                         'MakeID:3 AND BodyStyle1:6' => 0,
                         'MakeID:5 AND BodyStyle1:6' => 0,
                         'MakeID:4 AND BodyStyle1:6' => 12,
                         'SteeringID:13 AND BodyStyle1:6' => 0,
                         'TransmissionID:6 AND BodyStyle1:6' => 10
                       }
                     }
                   })
    end

    it 'returns list internal link including model recommendation' do
      result = subject.call
      recommended_model_section = result.find { |r| r[:title] == 'Recommended model of Sedan' }

      expect(recommended_model_section).not_to be_nil
      expect(recommended_model_section[:condition].first.first[0]).to eq('used_car/toyota/camry/')
      expect(recommended_model_section[:condition].first.first[1]).to eq('Toyota Camry')
    end

    it 'returns list internal link including japan make recommendation' do
      result = subject.call

      recommended_model_section = result.find { |r| r[:title] == 'Make x Sedan' }
      expect(recommended_model_section).not_to be_nil
      expect(recommended_model_section[:condition].count).to eq(3)
      expect(recommended_model_section[:condition].second.first[0]).to eq('used_car/nissan/all/?bsty=6')
      expect(recommended_model_section[:condition].second.first[1]).to eq('Nissan x Sedan')
    end

    it 'returns list internal link including price recommendation' do
      result = subject.call

      recommended_model_section = result.find { |r| r[:title] == 'Sedan x Price (FOB-US$)' }

      expect(recommended_model_section).not_to be_nil
      expect(recommended_model_section[:condition].second.first[0]).to eq('used_car/all/all/?prcf=500&prct=1000&bsty=6')
      expect(recommended_model_section[:condition].second.first[1]).to eq('Sedan x US$500-1,000')
    end

    it 'returns list internal link including option recommendation' do
      result = subject.call

      recommended_model_section = result.find { |r| r[:title] == 'Sedan x Option' }
      expect(recommended_model_section).not_to be_nil
      expect(recommended_model_section[:condition].first.first[0]).to eq('used_car/all/all/?tmns=6&bsty=6')
      expect(recommended_model_section[:condition].first.first[1]).to eq('Sedan x Manual')
    end
  end
end
