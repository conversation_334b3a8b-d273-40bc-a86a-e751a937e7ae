section.footer-link-area.me-24
  h2.footer-link-block-ttl.d-inline-block Buy
  .footer-link-block-wrap.footer-link-block-wrap--left
    section.footer-category
      h3.footer-category-ttl Search from Stock
      ul.footer-link-block
        li.footer-link-item-ttl
          = link_to tcv_url(path: 'used_car/all/all/'), class: 'footer--link' do
            span.ico.icon-chevron-right
            | Search All from Stock
        li.footer-link-item.mt-16
          section.footer-link-area-child
            h4.footer-link-block-child-ttl Search By Make
            ul.footer-link-block-child
              - @maker_data.each do |maker|
                li.footer-link-item
                  = link_to tcv_url(path: maker[:url]), class: 'footer--link' do
                    span.ico.icon-chevron-right
                    span.fw-bold = maker[:name]
                    span.car_amount = " #{stock_count_format(maker[:stock])}"

      ul.footer-link-block
        li.footer-link-item
          section.footer-link-area-child
            h4.footer-link-block-child-ttl Car Price (FOB-US$)
            ul.footer-link-block-child
              - @master_fobs.each do |fob|
                li.footer-link-item
                  = link_to fob.pc_url, class: 'fw-bold footer--link' do
                    span.ico.icon-chevron-right.fw-bold
                    = fob.name

          li.footer-link-item
            section.footer-link-area-child
              h4.footer-link-block-child-ttl Search By BodyStyle
              ul.footer-link-block-child
                - @body_style_data.each do |body|
                  li.footer-link-item
                    = link_to tcv_url(path: body[:url_with_float_id]), class: 'fw-bold footer--link' do
                      span.ico.icon-chevron-right
                      = body[:name]

      ul.footer-link-block
        li.footer-link-item
          section.footer-link-area-child
            h4.footer-link-block-child-ttl Other Categories
            ul.footer-link-block-child
              - @category_data.each do |category|
                li.footer-link-item
                  = link_to tcv_url(path: category[:pc_path]), class: 'fw-bold footer--link' do
                    span.ico.icon-chevron-right.fw-bold
                    = category[:name]

          li.footer-link-item
            section.footer-link-area-child
              h4.footer-link-block-child-ttl Vehicles In Stock
              ul.footer-link-block-child
                - @vehicle_stock_not_kenya.each do |country|
                  li.footer-link-item
                    = link_to country.pc_url, class: 'fw-bold footer--link' do
                      span.ico.icon-chevron-right
                      / 410 is korea country code
                      = country.number == 410 ? 'Korea' : country.name

          li.footer-link-item
            section.footer-link-area-child
              h4.footer-link-block-child-ttl Local Page
              ul.footer-link-block-child
                - @all_region.each do |region|
                  li.footer-link-item
                    = link_to local_page_path(region.name.delete(' ')), class: 'fw-bold footer--link' do
                      span.ico.icon-chevron-right
                      = region.name

          li.footer-link-item
            section.footer-link-area-child
              h4.footer-link-block-child-ttl Search By Dealer
              ul.footer-link-block-child
                li.footer-link-item
                  = link_to tcv_url(path: Settings.footer_links.searcher.search_by_dealer), class: 'fw-bold footer--link' do
                    span.ico.icon-chevron-right
                    | Search By Dealer
