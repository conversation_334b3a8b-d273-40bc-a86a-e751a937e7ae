- if recommend_make_model.present?
  .recommend-title #{@car_make.vc_name_e}'s recommended models
  ul.recommend-link
    - recommend_make_model.each do |model_name|
      li.item
        = link_to search_path_with_trailing_slash(make: @car_make.vc_name_e.downcase, model: escape_param(model_name.downcase)) do
         span #{@car_make.vc_name_e} #{model_name}
         span.ico.icon-chevron-right
- if recommend_body_style_list.present?
  .recommend-title List by body type of #{@car_make.vc_name_e}
  ul.recommend-link
    - recommend_body_style_list.each do |body_style|
      li.item
        = link_to search_path_with_trailing_slash(make: @car_make.vc_name_e.downcase, model: :all, bsty: body_style.primary_body_style_id) do
          span #{body_style.name}
          span.ico.icon-chevron-right
