module Search
  class ValidateParamsService
    class << self
      BOOLEAN_VALIDATE = %w[0 1].freeze
      FLOAT_REGEX = /^[-+]?[0-9]+\.[0-9]+$/
      VALID_PARAMS = %w[prcf prct fd fid jid smo emo mimn mimx sds eds ac st nw spp dr do rfc tmns fues
                        ecls bsty make model controller action pn si co uid eid pb addfvo gfr utm_source
                        utm_medium olddomain fbclid gv so ignfs tp hp gad flt wbraid ct lp ssp darkschemeovr
                        setlang pgcl general_portal_token tm fu mdrv tag _ga gclid utm_campaign
                        utm_content utm_term wipn wirc wfrc _gl fcd isNew mi sb op].freeze
      attr_accessor :params, :car_counter_instance

      def valid_params(params, car_counter_instance)
        self.params = params
        self.car_counter_instance = car_counter_instance

        whitelist_params && check_prcf && check_prct && check_rfc && check_make && check_model && check_fid && check_jid &&
          check_smo && check_emo && check_mimn && check_mimx && check_sds && check_eds && check_ac && check_st && check_nw &&
          check_spp && check_dr && check_do && check_tmns && check_fues && check_ecls && check_bsty? && check_co && check_op
      end

      private

      def check_integer?(str_number)
        str_number.to_i.to_s == str_number
      end

      def whitelist_params
        (params.keys - VALID_PARAMS).none?
      end

      def check_prcf
        return true if params[:prcf].nil?
        return false unless check_integer?(params[:prcf])

        fob_options_data = value_from_array(default_master_data[:fob_options_data])
        fob_options_data.include?(params[:prcf].to_i)
      end

      def check_prct
        return true if params[:prct].nil?
        return false unless check_integer?(params[:prct])

        fob_options_data = value_from_array(default_master_data[:fob_options_data])
        fob_options_data.include?(params[:prct].to_i)
      end

      def check_rfc
        return true if params[:rfc].nil?

        BOOLEAN_VALIDATE.include?(params[:rfc])
      end

      def check_make
        return true if params[:make].nil? || params[:make] == 'all'

        master_make_name_list.include?(params[:make].downcase)
      end

      def check_model
        return true if params[:model].nil? || params[:model] == 'all'

        MasterMake.joins(:master_models)
          .where('LOWER(master_makes.vc_name_e) = ? AND LOWER(master_models.vc_name_e) = ? AND master_models.is_valid = 1
                                                                                           AND master_makes.is_valid = 1',
                 params[:make].downcase, params[:model].downcase)
          .take.present?
      end

      def check_fid
        return true if params[:fid].nil?
        return false unless check_integer?(params[:fid])

        regis_year = value_from_array(default_master_data[:regis_year])
        regis_year.include?(params[:fid].to_i)
      end

      def check_jid
        return true if params[:jid].nil?
        return false unless check_integer?(params[:jid])

        regis_year = value_from_array(default_master_data[:regis_year])
        regis_year.include?(params[:jid].to_i)
      end

      def check_smo
        return true if params[:smo].nil?
        return false unless check_integer?(params[:smo])

        regis_year = value_from_array(default_master_data[:regis_month])
        regis_year.include?(params[:smo].to_i)
      end

      def check_emo
        return true if params[:emo].nil?
        return false unless check_integer?(params[:emo])

        regis_month = value_from_array(default_master_data[:regis_month])
        regis_month.include?(params[:emo].to_i)
      end

      def check_mimn
        return true if params[:mimn].nil?
        return false unless check_integer?(params[:mimn])

        mileage_options = value_from_array(default_master_data[:mileage_options])
        mileage_options.include?(params[:mimn].to_i)
      end

      def check_mimx
        return true if params[:mimx].nil?
        return false unless check_integer?(params[:mimx])

        mileage_options = value_from_array(default_master_data[:mileage_options])
        mileage_options.include?(params[:mimx].to_i)
      end

      def check_sds
        return true if params[:sds].nil?
        return false unless check_integer?(params[:sds])

        engine_capacity_options_data = value_from_array(default_master_data[:engine_capacity_options_data])
        engine_capacity_options_data.include?(params[:sds].to_i)
      end

      def check_eds
        return true if params[:eds].nil?
        return false unless check_integer?(params[:eds])

        engine_capacity_options_data = value_from_array(default_master_data[:engine_capacity_options_data])
        engine_capacity_options_data.include?(params[:eds].to_i)
      end

      def check_ac
        return true if params[:ac].nil?
        return false unless check_integer?(params[:ac])

        accident_options = value_from_array(default_master_data[:accident_options])
        accident_options.include?(params[:ac].to_i)
      end

      def check_st
        return true if params[:st].nil?
        return false unless check_integer?(params[:st])

        steering_options = value_from_array(default_master_data[:steering_options])
        steering_options.include?(params[:st].to_i)
      end

      def check_nw
        return true if params[:nw].nil?

        BOOLEAN_VALIDATE.include?(params[:nw])
      end

      def check_spp
        return true if params[:spp].nil?

        BOOLEAN_VALIDATE.include?(params[:spp])
      end

      def check_dr
        return true if params[:dr].nil?
        return false unless check_integer?(params[:dr])

        driver_type_options = value_from_array(default_master_data[:driver_type_options])
        driver_type_options.include?(params[:dr].to_i)
      end

      def check_do
        return true if params[:do].nil?
        return false unless check_integer?(params[:do])

        any_door_options = value_from_array(default_master_data[:any_door_options])
        any_door_options.include?(params[:do].to_i)
      end

      def check_tmns
        return true if params[:tmns].nil?

        current_tmns = params[:tmns].split('*')
        return false if current_tmns.any? { |item| !check_integer?(item) }

        transmission_options = value_from_array(default_master_data[:transmission_options])
        (current_tmns.map(&:to_i) - transmission_options).none?
      end

      def check_fues
        return true if params[:fues].nil?

        current_fues = params[:fues].split('*')
        return false if current_fues.any? { |item| !check_integer?(item) }

        fuel_type_options = value_from_array(default_master_data[:fuel_type_options])
        (current_fues.map(&:to_i) - fuel_type_options).none?
      end

      def check_ecls
        return true if params[:ecls].nil?

        current_ecls = params[:ecls].split('*')
        return false if current_ecls.any? { |item| !check_integer?(item) }

        color_options = value_from_array(default_master_data[:color_options])
        (current_ecls.map(&:to_i) - color_options).none?
      end

      def check_bsty?
        return true if params[:bsty].nil?

        current_bsty = params[:bsty].split('*')

        integer_array = current_bsty.select { |item| (item =~ FLOAT_REGEX).nil? }
        return false if integer_array.any? { |item| !check_integer?(item) }

        body_style_options = value_from_array(default_master_data[:body_style_options])
        return false if (integer_array.map(&:to_i) - body_style_options).present?

        secondary_body_style_options = default_master_data[:secondary_body_style_options]
        float_array = current_bsty - integer_array

        float_array.each do |bsty|
          main_bsty, second_bsty = bsty.split('.')
          return true if second_bsty == '0' && body_style_options.include?(main_bsty.to_i)
          return false if !check_integer?(main_bsty) || !check_integer?(second_bsty)

          result = secondary_body_style_options.detect { |item| item[2] == main_bsty.to_i && item[1] == second_bsty.to_i }
          return false if result.nil?
        end

        true
      end

      def check_co
        return true if params[:co].nil?
        return false unless check_integer?(params[:co])

        country_number_list.include?(params[:co].to_i)
      end

      def default_master_data
        @default_master_data ||= Search::MasterDataService.master_data(car_counter_instance)
      end

      def value_from_array(data)
        data.map(&:last)
      end

      def country_number_list
        Rails.cache.fetch('country_number_list', expires_in: 1.hour) do
          MCountry.pluck(:number)
        end
      end

      def master_make_name_list
        Rails.cache.fetch('master_make_name_list', expires_in: 3.hour) do
          MasterMake.pluck(:vc_name_e).map(&:downcase)
        end
      end

      def check_op
        return true if params[:op].nil?
        return false unless check_integer?(params[:op])

        MasterInfo::CarOption.find_by(query_value: params[:op]).present?
      end
    end
  end
end
