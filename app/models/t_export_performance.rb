class TExportPerformance < ApplicationRecord
  self.primary_key = :t_export_performance_id

  validates :create_date, :update_date, :stock_date, presence: true
  validates :make_id, :model_id, presence: true, numericality: { only_integer: true }
  validates :all_money_receive_count, :make_money_receive_count, :model_money_receive_count,
            presence: true, numericality: { only_integer: true }
  class << self
    def export_performance_12_months(make_id, model_id)
      start_date = 12.months.ago.beginning_of_month
      end_date = Date.today.beginning_of_month

      where(make_id: make_id, model_id: model_id, stock_date: start_date..end_date)
        .order(stock_date: :desc, update_date: :desc)
        .group_by(&:stock_date)
        .map { |_, records| records.first }
    end
  end
end
