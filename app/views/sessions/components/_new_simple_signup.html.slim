.sign-up_area--new.d-none data-controller='signup'
  .switch-to-signin-area
    h2.tab-content_tittle--signup Sign up
    .descript-area.sign-up
      span.descript-area__body Do you have an account?
      span.descript-tab-link
        a data-action='click->signup#openSignInTab'
          span.btn-main-txt Log in
  = form_with url: 'javascript:;', method: :get do |f|
    .register-area__content
      .contactus-area
        span.text If you have any problems, please contact&nbsp
        span.contact-link
          = link_to 'here', '/help/contactus/'
        | .
      .body
        .content-field
          span.staff-icon
          span.arrow
          span.content.guide-box
            .guide-text Thank you for your inquiry. I will support your purchase.
        .content-field.email
          span.content.guide-box
            .guide-text Please tell me your email address.
        .content-field.input-field.email-input-field.d-none data-signup-target='emailInputField'
          span.content.input
            .input-text
            .edit-icon data-action='click->signup#showInputBox' data-field='email'
        .ok-message.d-none OK
        .input-box.email-input-box data-signup-target='emailInputBox'
          span.title Please enter your email address.
          = f.text_field :email, { required: true,
                                   class: 'input',
                                   'data-action': 'blur->signup#validateField keydown->signup#handleKeyPress',
                                   'data-input-field': :email,
                                   'data-signup-target': :ipEmailTxt,
                                   'data-next-target': 'phoneNumChatBox',
                                   'autocomplete': 'new-email',
                                   placeholder: '<EMAIL>' }
          .error-message.mt-5
            span.exclamation-mark !
            span.message Please enter a valid Email address.

        .chatbox data-signup-target='phoneNumChatBox'
          .content-field
            span.staff-icon
            span.arrow
            span.content.guide-box
              .guide-text Please tell me your phone number.
          .content-field.input-field.number-input-field.d-none data-signup-target='phoneNumInputField'
            span.content.input
              .input-text
              .edit-icon data-action='click->signup#showInputBox' data-field='phoneNum'
          .ok-message.d-none OK
          .input-box.number-input-box.input--select data-signup-target='phoneNumInputBox'
            span.title Please enter your phone number.
            = f.select :country_code, options_for_select(country_code_options(@country_list), user_country_code),
                                      { include_blank: false },
                                      { class: 'input country-code-input mb-10', 'data-signup-target' => 'countryNum' }
            = f.text_field :number, { required: true,
                                      type: :tel,
                                      maxlength: 15,
                                      'data-action': 'blur->signup#validateField keydown->signup#handleKeyPress',
                                      'data-input-field': :number,
                                      'data-signup-target': :ipNumberTxt,
                                      'data-next-target': 'passwordChatBox',
                                      'autocomplete': 'new-number',
                                      class: 'input',
                                      placeholder: 'Phone Number' }
            .error-message.mt-5
              span.exclamation-mark !
              span.message Please enter numeric values only (No space, No symbols). 7 - 15 digits are required.

        .chatbox data-signup-target='passwordChatBox'
          .content-field
            span.staff-icon
            span.arrow
            span.content.guide-box
              .guide-text Please tell me the password to set.
          .content-field.input-field.password-input-field.d-none data-signup-target='passwordInputField'
            span.content.input.input--password
              .input-text
              .edit-icon data-action='click->signup#showInputBox' data-field='password'
            .custom-cb.show_password_field
              input#show_password_field type='checkbox'
              label.fs-13 for='show_password_field' data-action='click->signup#showPasswordField' Show password
          .ok-message.ok-password-message.d-none OK
          .input-box.password-input-box data-signup-target='passwordInputBox'
            span.title Please enter the password you wish to set.
            = f.text_field :password, { required: true,
                                        type: :password,
                                        class: 'input mb-10',
                                        maxlength: 20,
                                        'data-action': 'blur->signup#validateField keydown->signup#handleKeyPress',
                                        'data-input-field': 'password',
                                        'data-signup-target': :ipPasswordTxt,
                                        'data-next-target': 'countryChatBox,purposeChatBox',
                                        'autocomplete': 'new-password',
                                        placeholder: '********' }
            .error-message.mt-5
              span.exclamation-mark !
              span.message Use 6~20 characters. Both letters and numbers are needed.
            .custom-cb.show_password
              input#show_password type='checkbox'
              label.vehicle-search-modal-checkbox-wrap.fl-right for='show_password' data-action='click->signup#showPassword' Show password

        .chatbox data-signup-target='countryChatBox'
          .content-field
            span.staff-icon
            span.arrow
            span.content.guide-box
              .guide-text What country do you live in?
          .content-field.input-field.country-input-field data-signup-target='countryInputField'
            span.content.input
              .input-text = current_country[:country_name]
              .edit-icon data-action='click->signup#showInputBox' data-field='country'
          .ok-message.d-none OK
          .input-box.country-input-box.input--select.d-none data-signup-target='countryInputBox'
            span.title Please enter your country.
            = f.select :country, options_for_select(country_options(@country_list), user_country_code),
                                 { include_blank: false },
                                 { class: 'input country-code-input',
                                   'data-signup-target': 'countryCode',
                                   'data-action': 'blur->signup#validateField change->signup#validateField',
                                   'data-input-field': 'country',
                                   'autocomplete': 'new-country',
                                   'data-next-target': 'purposeChatBox' }
            .error-message.mt-5
              span.exclamation-mark !
              span.message Please select your country.

        .chatbox data-signup-target='purposeChatBox'
          .content-field
            span.staff-icon
            span.arrow
            span.content.guide-box
              .guide-text What is the purpose of your car?
          .content-field.input-field.d-none data-signup-target='purposeInputField'
            span.content.input
              .input-text
              .edit-icon data-action='click->signup#showInputBox' data-field='purpose'
          .ok-message.d-none OK
          .input-box data-signup-target='purposeInputBox'
            span.title Please choose the one that applies.
            .purpose-wrap
              input.d-none id='personal-use' name='purpose' value='3' type='radio' data-signup-target='ipPurposeTxt'
              label.me-5 for='personal-use' data-signup-target='personalUse' data-next-target='grecaptchaChatbox' data-action='click->signup#choosePurpose' data-purpose='personalUse' For Personal Use
              input.d-none id='for-customer' name='purpose' value='1' type='radio' data-signup-target='ipPurposeTxt'
              label for='for-customer' data-signup-target='forCustomer' data-next-target='grecaptchaChatbox' data-action='click->signup#choosePurpose' For Customer
            .d-none.input.input-purpose
            .error-message.mt-5
              span.exclamation-mark !
              span.message Please select your purpose.

        .chatbox.recaptcha-chatbox data-signup-target='grecaptchaChatbox'
          .content-field
            span.staff-icon
            span.arrow
            span.content.guide-box
              .guide-text If you are not a robot, please check the below.
          .captcha-wrap
            .g-recaptcha data-sitekey="#{ Settings.recaptcha_sitekey }" data-callback='grecaptchaCallback'
        .send-button-wrap
          = f.submit 'Sign up', { class: 'send-button fw--large inactive',
                                  'data-action': 'click->signup#validateFieldWhenSubmitting',
                                  'disabled': 'disabled' }
          .gcaptcha-error-message
              .error-message__body
                span.exclamation-mark !
                span.message data-signup-target='errorMessage' Authentification failed. Please try again.
