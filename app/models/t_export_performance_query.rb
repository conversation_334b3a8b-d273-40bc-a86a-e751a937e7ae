class TExportPerformanceQuery < ApplicationRecord
  self.primary_key = :t_export_performance_id

  validates :create_date, :update_date, :collection_month, presence: true
  validates :param_value, presence: true, numericality: { only_integer: true }
  validates :all_count, :individual_count, presence: true, numericality: { only_integer: true }
  class << self
    def export_performance_12_months(param_name, param_value)
      start_date = 12.months.ago.beginning_of_month
      end_date = Date.today.beginning_of_month

      where(param_name:, param_value:, collection_month: start_date..end_date)
        .order(collection_month: :desc, update_date: :desc)
        .group_by(&:collection_month)
        .map { |_, records| records.first }
    end
  end
end
