.top-three
  ul
    li.make = link_to search_path_with_trailing_slash(make: car[:make]&.downcase, model: 'all'), target: '_blank' do
      span = car[:make]
    li.make = link_to search_path_with_trailing_slash(make: car[:make]&.downcase, model: car[:model]&.downcase), target: '_blank' do
      span = car[:model]
    li.more
      span.icon-chevron-right
      = link_to search_path_with_trailing_slash(make: car[:make]&.downcase, model: car[:model]&.downcase), target: '_blank' do
        span More(#{car[:count]})

.rank
  - if car[:top_models].present?
    ul
      - car[:top_models].each do |car_detail|
        li.wrapper
          .first-car = link_to car_detail_path(make: car[:make]&.downcase, model: car[:model]&.downcase, car_id: car_detail[:car]['ItemID']), target: '_blank' do
            = car_img_tag(car_detail[:car], alt: '', type: 'crop', height: 100, width: 134, loading: 'lazy')
            span = car_detail[:fob_price]
  - else
    p Sorry. Currently, there is no corresponding vehicle inventory.
    p Please search by entering the search conditions, or contact TCV about the vehicle.
