module Import
  module MasterData
    class ActionImport
      require 'google/cloud/storage'
      SPECIAL_REGEX = /"(.*?)”/

      attr_reader :import_batch, :logger, :file_encoding, :headers, :cloud_storage, :failed_instances

      def initialize(configs)
        @cloud_storage    = Extensions::GoogleCloud::Storage.new(configs[:file_name], configs[:local_download_folder])
        @file_encoding    = configs[:file_encoding] || Encoding::UTF_8
        @import_batch     = configs[:import_batch]
        @headers          = configs[:headers].nil?
        @logger           = configs[:logger] || ZgcpToolkit::Logger.new("#{self.class.name.downcase.split('::').join('_')}_log")
        @failed_instances = []
      end

      def call
        cloud_storage.download unless ENV['MANUAL_TEST']
        line_counter.call
        fix_special_chars(local_file_path)

        SmartCsv.batch_read?(local_file_path, csv_read_options) do |rows|
          data    = headers ? rows.map(&:to_h) : rows
          results = import(data)
          failed_instances + results.failed_instances
        end

        failed_instance_handler.call
        remove_import_file unless ENV['MANUAL_TEST']
      end

      private

      def fix_special_chars(local_file_path)
        file = File.read(local_file_path)

        char_index = file.index(SPECIAL_REGEX)
        return if char_index.nil?

        while char_index.present?
          file[char_index] = '“'
          char_index = file.index(SPECIAL_REGEX)
        end

        file += "\r\n" unless file.last(2) == "\r\n"

        File.open(local_file_path, 'w') { |new_file| new_file.puts file }
      end

      def import
        raise StandardError, "You must implement #{self.class}##{__method__}"
      end

      def line_counter
        proc do
          lines_count = count_non_empty_lines(local_file_path)

          if lines_count <= 1
            logger.warn "File data #{cloud_storage.file_name} is empty. Nothing to import. Exit"
            remove_import_file unless ENV['MANUAL_TEST']
            return
          end
        end
      end

      def failed_instance_handler
        proc do
          if failed_instances.any?
            detailed_errors = failed_instances.map(&:errors).group_by(&:full_messages).map do |messages, examples|
              "Error: #{messages.join('. ')}\nSamples:\n#{examples.sample(3).map(&:inspect).join("\n")}\n..."
            end.join("\n\n")
            logger.error "Bulk Import -- Failed Instances: #{failed_instances.length};\n#{detailed_errors}"
          end
        end
      end

      def local_file_path
        cloud_storage.local_file_path
      end

      def count_non_empty_lines(file_path)
        `cat #{file_path.shellescape} | sed '/^\\s*$/d' | wc -l`.to_i
      end

      def remove_import_file
        FileUtils.rm_rf(local_file_path)
      end

      def csv_read_options
        options             = { headers: headers, liberal_parsing: true, encoding: file_encoding }
        options[:col_sep]   = "\t" if file_type == 'tsv'
        options
      end

      def file_type
        local_file_path.match?(/\.(tsv)\z/) ? 'tsv' : 'csv'
      end
    end
  end
end
