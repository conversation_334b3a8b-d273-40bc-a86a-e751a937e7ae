- if recommend_make_model.present?
  .recommend-title #{@car_make.vc_name_e}'s recommended model
  ul.similar-link.mt-25
    - recommend_make_model.each do |model_name|
      li.item
        span.ico.icon-chevron-right
        = link_to "#{@car_make.vc_name_e} #{model_name}", search_path_with_trailing_slash(make: @car_make.vc_name_e.downcase, model: escape_param(model_name.downcase))
- if recommend_body_style_list.present?
  .recommend-title List by body type of #{@car_make.vc_name_e}
  ul.similar-link.mt-25
    - recommend_body_style_list.each do |body_style|
      li.item
        span.ico.icon-chevron-right
        = link_to body_style.name, search_path_with_trailing_slash(make: @car_make.vc_name_e.downcase, model: :all, bsty: body_style.primary_body_style_id)
