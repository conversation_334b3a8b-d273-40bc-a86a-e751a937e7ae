.other
  table.mt-20
    tbody
      - other_top_cars.each_with_index do |car, index|
        tr
          td.order-number
            = index + 4
          td.make-name
            = link_to search_path_with_trailing_slash(make: car[:make]&.downcase, model: 'all'), target: '_blank' do
              span = car[:make]
          td.model-name
            = link_to search_path_with_trailing_slash(make: car[:make]&.downcase, model: car[:model]&.downcase), target: '_blank' do
              span = car[:model]
          td.more
            = link_to search_path_with_trailing_slash(make: car[:make]&.downcase, model: car[:model]&.downcase), target: '_blank' do
              span More(#{car[:count]})
