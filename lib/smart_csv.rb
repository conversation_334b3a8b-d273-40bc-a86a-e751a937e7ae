require 'csv'

class SmartCsv < CSV
  DEFAULT_BATCH_SIZE = 500

  def self.batch_read?(file_path, options = {})
    rows = []
    delete_rows = []
    batch_size = options.delete(:batch_size) || DEFAULT_BATCH_SIZE

    CSV.foreach(file_path, **options) do |row|
      delete_flag = row.delete('delete').to_a
      if delete_flag[1].to_s == '1'
        delete_rows << row
        next
      end

      rows << row
      if rows.length == batch_size
        yield(rows, delete_rows) if block_given?
        rows = []
      end
    end
    yield(rows, delete_rows) if block_given? && rows.size.positive?
    true
  end
end
