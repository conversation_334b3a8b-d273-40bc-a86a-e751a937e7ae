- favorite_action = user_signed_in? && 'addFavorite' || 'showModalSignIn'
- est_class = ship_estimated? && 'estimated' || 'not_estimate'
- not_pr_car = @cars.find { |c| c['StockPR'].nil? }

- if not_pr_car
  - content_for :serp_thumbnail do
    = render 'shared/pagemap_thumbnail', thumb_url: car_img_url(not_pr_car, width: 120, height: 120, type: 'crop')

.vehicle__list-area.mb-5 data-search-target='mainList'
  - show_affiliate_banner = affiliate_banner?
  - stock_pr_count = @cars.count { |car| car['StockPR'].present? } if show_affiliate_banner
  - @cars.each.with_index do |car, index|
    / Ensure that the image above the fold is not lazy-loaded to improve Lighthouse performance
    = render partial: 'search/cars/item',
              locals: { favorite_action: favorite_action, inquiry_counter: @inquiry_counter, est_class: est_class, car: car, no_lazy: index < 1, car_counter: index, include_index: true }
    - if stock_pr_count.present? && display_affiliate_banner?((index + 1), stock_pr_count)
        = render 'shared/affiliate_banner', platform: :pc

.pagination-block
  - if enable_paginate?
    - left = params[:pn].to_i > 2 ? 2 : 5
    = paginate @paginatable_array, left: left, link_attributes: { aria: { label: 'Pagination Navigation' } }
  - else
    nav.pagination
      span.prev.prev-button
        a rel='prev' aria-label='Previous Page'
      span.page class="#{params[:pn].to_i.nonzero? ? '' : 'current'}" 1
      span.next.next-button
        a rel='next' aria-label='Next Page'
