.ranking-title
  span.ico.icon-crown
  span.title
    | Browse by Popular Ranking
.ranking-list
  - @popular_ranking.each_with_index do |ranking, index|
    .list__item
      = link_to search_path_with_trailing_slash(make: ranking[:maker_name].downcase, model: ranking[:model_name].downcase) do
        .item__img-wrap
          - if ranking[:img_url].present?
            = image_tag ranking[:img_url], width: 113, height: 70, alt: ranking[:model_name]
          - else
            = image_pack_tag 'media/images/commons/no-image.webp', width: 113, height: 70, alt: 'no image'
        .item__title
          span.rank
            = index + 1
          span.brand-name
            = "#{ranking[:maker_name]} #{ranking[:model_name]}"
            span.total
              = "(#{ranking[:total_count].to_i.zero? ? '-' : ranking[:total_count]})"
