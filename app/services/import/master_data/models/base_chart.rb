module Import
  module MasterData
    module Models
      class BaseChart < ::Import::MasterData::ActionImport
        private

        def import(data)
          data.map! do |datum|
            new_hash = {}
            datum.each { |k, v| new_hash[k.underscore] = v }
            new_hash
          end

          model_class.import(
            data,
            on_duplicate_key_update: model_class.attribute_names - [id_column, 'create_date'],
            batch_size: import_batch,
          )
        end

        def model_class
          raise NotImplementedError, 'Subclasses must implement model_class'
        end

        def id_column
          raise NotImplementedError, 'Subclasses must implement id_column'
        end
      end
    end
  end
end
