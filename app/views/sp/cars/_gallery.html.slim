#first-car-image
  = image_tag @car_presenter.img_urls.first.first, alt: @car_presenter.name, fetchpriority: 'high'

#car-carousel.f-carousel.d-none
  .number-block
  - @car_presenter.img_urls.each_with_index do |photo, index|
    .f-carousel__slide data-thumb-src='#{photo[1]}?height=90&width=140&type=resize' data-fancybox='gallery' data-src="#{photo[0]}"
      - if index.zero?
        = image_tag photo[0], alt: @car_presenter.name, fetchpriority: 'high'
      - else
        = image_tag '', alt: @car_presenter.name, data: { 'lazy-src': photo[0] }
