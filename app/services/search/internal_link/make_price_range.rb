module Search
  module InternalLink
    class MakePriceRange < BaseService
      TOP_POPULAR_MODELS = 5

      SECTIONS = {
        popular_model: :handle_popular_model,
        varying_prices: :handle_varying_prices,
        varying_body_type: :handle_varying_body_type,
        varying_category: :handle_varying_category
      }.freeze

      def initialize(make, price_params)
        super()
        @make = make
        @make_name = make.vc_name_e
        @make_id = make.id_make
        @price_range_query = price_params.to_query
        @popular_for_all_make_service = Search::GetPopularForAllMakeService.new
      end

      private

      def show_internal_link?
        @make.present? && price_range.present?
      end

      def cache_key
        "internal_links_make_price_range_#{@make_name}_#{@price_range_query}"
      end

      def price_range
        @make_price_range ||= MasterInfo::InternalLink::PriceRange.find_by(query: @price_range_query)
      end

      def handle_popular_model
        return if popular_models.blank?

        condition = build_condition_data(popular_models, 'popular_model') do |model|
          ["used_car/#{model.maker_nm.downcase}/#{model.model_nm.downcase}/?#{@price_range_query}", "#{model.maker_nm} #{model.model_nm}"]
        end
        { title: "#{@make_name} x #{price_range[:text]} Popular Models", condition: condition }
      end

      def handle_varying_prices
        other_price_ranges = @price_range.reject { |range| range[:query] == @price_range_query }
        condition = build_condition_data(other_price_ranges, 'price_range') do |range|
          ["used_car/#{@make_name.downcase}/all/?#{range[:query]}", "#{@make_name} x #{range[:text]}"]
        end
        { title: 'Same Brand, Varying Prices', condition: condition }
      end

      def handle_varying_body_type
        condition = build_condition_data(@body_style, 'body_style') do |body_style|
          ["used_car/#{@make_name.downcase}/all/?#{@price_range_query}&#{body_style[:query]}",
           "#{@make_name} x #{price_range[:text]} x #{body_style[:text]}"]
        end
        { title: 'Same Brand and Prices, Varying Body Type', condition: condition }
      end

      def handle_varying_category
        condition = build_condition_data(@option, 'category') do |option|
          ["used_car/#{@make_name.downcase}/all/?#{@price_range_query}&#{option[:query]}", "#{@make_name} x #{price_range[:text]} x #{option[:text]}"]
        end
        { title: 'Same Brand and Price Tier, Varying Category', condition: condition }
      end

      def combined_facet_queries
        queries = []

        popular_models.each_with_index do |model, index|
          queries << ["popular_model_#{index}", "MakeID:#{model.maker_id} AND ModelID:#{model.model_id} AND Price:#{price_range[:value]}"]
        end

        other_price_ranges = @price_range.reject { |range| range[:query] == @price_range_query }
        other_price_ranges.each_with_index do |range, index|
          queries << ["price_range_#{index}", "MakeID:#{@make_id} AND Price:#{range[:value]}"]
        end

        @body_style.each_with_index do |body_style, index|
          queries << ["body_style_#{index}", "MakeID:#{@make_id} AND BodyStyle1:#{body_style[:id]} AND Price:#{price_range[:value]}"]
        end

        @option.each_with_index do |option, index|
          queries << ["category_#{index}", "MakeID:#{@make_id} AND #{option[:solr_query]} AND Price:#{price_range[:value]}"]
        end

        queries.to_h
      end

      def popular_models
        @popular_models_make_price_range ||= @popular_for_all_make_service.list_top_model_for_make_in_price_range(@make_id,
                                                                                                                  price_range[:master_info_id]) || []
      end
    end
  end
end
