source 'https://rubygems.org'
git_source(:github) { |repo| "https://github.com/#{repo}.git" }

ruby '3.2.2'

# Bundle edge Rails instead: gem 'rails', github: 'rails/rails', branch: 'main'
gem 'rails', '~> *******'
# Use MySQL as the database for Active Record
gem 'mysql2'
# Use Puma as the app server
gem 'puma', '~> 6.4', '>= 6.4.3'
# Use SCSS for stylesheets
gem 'sass-rails', '>= 6'
# Transpile app-like JavaScript. Read more: https://github.com/rails/webpacker
gem 'webpacker', '~> 5.0'
# Turbolinks makes navigating your web application faster. Read more: https://github.com/turbolinks/turbolinks
gem 'turbolinks', '~> 5'
# Build JSON APIs with ease. Read more: https://github.com/rails/jbuilder
gem 'jbuilder', '~> 2.7'
# Use Redis adapter to run Action Cable in production
# gem 'redis', '~> 4.0'
# Use Active Model has_secure_password
# gem 'bcrypt', '~> 3.1.7'

# Use Active Storage variant
# gem 'image_processing', '~> 1.2'

# Reduces boot times through caching; required in config/boot.rb
gem 'bootsnap', '>= 1.4.4', require: false

group :development, :test do
  # Call 'byebug' anywhere in the code to stop execution and get a debugger console
  gem 'byebug', platforms: %i[mri mingw x64_mingw]
  gem 'pry-byebug'
end

group :development do
  # Access an interactive console on exception pages or by calling 'console' anywhere in the code.
  gem 'web-console', '>= 4.1.0'
  # Display performance information such as SQL time and flame graphs for each request in your browser.
  # Can be configured to work on production as well see: https://github.com/MiniProfiler/rack-mini-profiler/blob/master/README.md
  gem 'listen', '~> 3.3'
  gem 'rack-mini-profiler', '~> 3.0'
  # Spring speeds up development by keeping your application running in the background. Read more: https://github.com/rails/spring
  gem 'spring'
  # Analyzing and formatting Ruby static code
  gem 'rubocop', '~> 1.76', require: false

  gem 'bundler-audit'
  gem 'brakeman'
end

group :test do
  # Adds support for Capybara system testing and selenium driver
  gem 'capybara', '>= 3.26'
  gem 'selenium-webdriver'
  # Easy installation and use of web drivers to run system tests with browsers
  gem 'webdrivers'

  # Gems for unit test
  gem 'rspec-rails', '~> 6.0', '>= 6.0.3'
  gem 'shoulda-matchers', '~> 5.3'
  gem 'faker', '~> 3.2', '>= 3.2.2'
  gem 'database_cleaner', '~> 2.0', '>= 2.0.2'
  gem 'factory_bot_rails', '~> 6.2'
  gem 'rails-controller-testing', '~> 1.0', '>= 1.0.5'
  gem 'simplecov', '~> 0.22.0'
end

# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem 'tzinfo-data', platforms: %i[mingw mswin x64_mingw jruby]

# Application ENV
gem 'figaro'

# Slim templates generator for Rails
gem 'slim-rails'

# Inline SVG icons
gem 'inline_svg'

# Metagem that includes google stackdriver logging
# + extra functionalities
gem 'stackdriver'
gem 'zgcp_toolkit'

# Using Google Cloud Task
gem 'google-cloud-tasks-v2'

# Application configuration
gem 'config'

# High performance Redis driver
gem 'hiredis', '~> 0.6.3'
gem 'redis', '~> 4.8', '>= 4.8.1', require: ['redis', 'redis/connection/hiredis']
gem 'redis-rails', '~> 5.0', '>= 5.0.2'

gem 'jpmobile'

gem 'psych', '~> 3.1'

# Accessing Google Cloud Storage for importing data
gem 'google-cloud-storage'

# Bulk inserting data using ActiveRecord
gem 'activerecord-import'

# To query Google Cloud IAM policy
gem 'google-cloud-resource_manager'

# Solr
gem 'rsolr',     '~> 2.3.0'
gem 'rsolr-ext', '~> 1.0.3'

gem 'crawler_detect'

gem 'active_hash', '~> 2.3.0'

gem 'google-iam-credentials-v1', '~> 0.4.0'

gem 'parallel', '~> 1.11', '>= 1.11.2'
gem 'kaminari', '~> 1.2.2'
# Meta tags
gem 'meta-tags', '~> 2.1'
# recaptcha
gem 'recaptcha'
# ab-test
gem 'split', '~> 4.0', '>= 4.0.3', require: 'split/dashboard'
gem 'redis-namespace', '~> 1.10'
# Newric Ruby Agent
gem 'newrelic_rpm', '~> 9.2'
gem 'newrelic-infinite_tracing', '~> 9.2'
#Faraday multipart
gem 'faraday-multipart', '~> 1.0', '>= 1.0.4'
gem "nokogiri", force_ruby_platform: true
gem 'rack', '~> 3.0'
gem 'rdoc', '~> 6.3', '>= *******'
gem 'uri', '~> 0.13.2'
