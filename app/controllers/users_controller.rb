class UsersController < ApplicationController
  def signin
    user = ::Users::Signin.exec(signin_params[:email], signin_params[:password])
    return render json: { msg: 'Unable to connect to the server, please try again.' }, status: :accepted if user.nil?

    save_cvpd(user.merge(remember_me: ActiveModel::Type::Boolean.new.cast(signin_params[:remember_me])))

    render json: { user: user, temporary_contact_id: params.permit(:ti) }, status: :ok
  end

  def signup
    recaptcha_result = verify_recaptcha(secret_key: Settings.recaptcha_secretkey, response: signup_params[:recaptcha_response])
    return render json: { user: { 'Message' => 'email-exist' } }, status: :ok unless recaptcha_result

    user = ::Users::Signup.new(signup_params).create
    return render json: { msg: 'Unable to connect to the server, please try again.' }, status: :accepted if user.nil?

    save_cvpd(user)

    render json: { user: user }, status: :ok
  end

  def shortcutmember
    result = ::Users::ShortcutMember.new(cookies['CVPD']).create

    render json: { result: result }, status: :ok
  end

  def user_signed_in
    render json: { is_signed_in: user_signed_in? }, status: :ok
  end

  private

  def signin_params
    @signin_params ||= params.permit(:email, :password, :remember_me)
  end

  def signup_params
    @signup_params ||= params.permit(:email, :country_code, :number, :purpose, :password, :country, :recaptcha_response)
  end
end
