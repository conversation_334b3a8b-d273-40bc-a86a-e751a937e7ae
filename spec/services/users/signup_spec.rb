require 'rails_helper'

RSpec.describe Users::Signup, type: :service do
  describe '#create' do
    let(:params) do
      {
        email: '<EMAIL>',
        password: 'password',
        country: '392',
        country_code: '704',
        number: '1234567890',
        purpose: '3',
        recaptcha_response: 'test_recaptcha_response'
      }
    end

    let(:signup) { described_class.new(params) }

    context 'when the signup is successful' do
      it 'returns the response body' do
        response_body = { 'UserID' => '123456', 'UserName' => 'test_user_name',
                          'UserHandleName' => 'test_user_name', 'UserUpdateDateTime' => '2024%2f01%2f05+16%3a08%3a18',
                          'UserLogonDate' => '2024%2f01%2f05+16%3a08%3a28', 'Hash' => 'AFE7010E3CD8461B63CE502FBB876DAD', 'Message' => 'success' }
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send)
          .and_return(instance_double(TcvCoreApi::Response, success?: true, body: response_body))

        result = signup.create
        expect(result).to eq(response_body)
      end
    end

    context 'when the signup is unsuccessful' do
      it 'returns nil' do
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(instance_double(TcvCoreApi::Response, success?: false))

        result = signup.create
        expect(result).to be_nil
      end
    end
  end
end
