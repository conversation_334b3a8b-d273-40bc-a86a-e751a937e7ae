class CustomLoggingMiddleware
  def initialize(app)
    @app = app
    @logger = ZgcpToolkit::Logger.new(:tcv_log)
  end

  def call(env)
    request = ActionDispatch::Request.new(env)

    start_time = Time.now
    remote_ip      = env['HTTP_X_FORWARDED_FOR']&.split(',')&.first.presence || request.remote_ip
    request_url    = safe_converter(request.url)
    referer        = safe_converter(request.referer)
    user_agent     = safe_converter(request.user_agent)
    request_method = safe_converter(request.request_method)

    status, headers, response = @app.call(env)
    latency = (Time.now - start_time).to_s

    message = "#{request_method} #{request_url} Completed #{status} in #{latency} seconds"
    @logger.info message, latency:, referer:, remote_ip:, request_method:, request_url:, user_agent:, status: status.to_s

    [status, headers, response]
  rescue StandardError => e
    @logger.error "#{e.message} \n #{e.backtrace.join("\n")}"
    @app.call(env)
  end

  private

  def safe_converter(string)
    return if string.nil?

    string.encode('UTF-8', invalid: :replace, undef: :replace, replace: '')
  end
end
