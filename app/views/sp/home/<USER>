.browse-by-popular-ranking-block
  h2.block__title
    span.ico.icon-crown
    | Browse by Popular ranking
  .block__list
    - @popular_ranking.each_with_index do |ranking, index|
      - alt_value = "#{ranking[:maker_name]} #{ranking[:model_name]}"
      .list__item
        = link_to search_path_with_trailing_slash(make: ranking[:maker_name].downcase, model: ranking[:model_name].downcase) do
          .item__info
            - if ranking[:img_url].present?
              = image_tag ranking[:img_url], width: 74, height: 46, alt: alt_value, loading: 'lazy'
            - else
              = image_pack_tag 'media/images/commons/no-image.webp', width: 74, height: 46, alt: alt_value, loading: 'lazy'
            span.info__rank_number
              = index + 1
            span.info__name
              = alt_value
              span.info__stock
                = "(#{ranking[:total_count].to_i.zero? ? '-' : ranking[:total_count]})"
          span.ico.icon-chevron-right
  .attention This ranking is based on the number of offers.
