module Search
  class GetPopularForAllMakeService
    TOP_ALL_MODEL = 5

    def list_all_model
      query_ranking_point(fetch_top_model)
    end

    def list_all_maker
      query_ranking_point(fetch_top_maker)
    end

    def list_all_model_in_price_range(price_range)
      query_ranking_point(fetch_top_model_in_price_range(price_range))
    end

    private

    def query_ranking_point(records)
      return if records.blank?

      TAggregateRankingPoint.query_by_make_and_model(records.pluck(:maker_id, :model_id), TOP_ALL_MODEL)
    end

    def fetch_top_model_in_price_range(price_range)
      TAggregateOffer.top_all_model_in_price_range(price_range, TOP_ALL_MODEL)
    end

    def fetch_top_model
      TAggregateOffer.top_all_model_by_offer_count.uniq(&:model_id).first(TOP_ALL_MODEL)
    end

    def fetch_top_maker
      TAggregateOffer.top_all_model_by_offer_count.uniq(&:maker_id).first(TOP_ALL_MODEL)
    end
  end
end
