require 'rails_helper'

RSpec.describe Search::MasterDataService do
  describe '#master_data' do
    let(:car_counter_instance) { instance_double(DataLoader::CarCounterService) }
    let(:maker_options_data) { [%w[Toyota toyota], %w[Honda honda]] }
    let(:model_options_data) { { 'toyota' => [%w[<PERSON><PERSON> camry], %w[Corolla corolla]] } }
    let(:article_data) do
      {
        'ColorType' => [
          MArticle.new(en: 'Red', article_id: 1),
          MArticle.new(en: 'Blue', article_id: 2),
        ],
        'Transmission' => [
          MArticle.new(en: 'Manual', article_id: 1),
          MArticle.new(en: 'Automatic', article_id: 2),
        ],
        'FuelType' => [
          MArticle.new(en: 'Petrol', article_id: 1),
          MArticle.new(en: 'Diesel', article_id: 2),
        ]
      }
    end
    let(:expected_regis_year) { (1960..Time.zone.now.year + 1).map { |year| [year, year] }.reverse }
    let(:expected_color_options) { [['Blue', 2], ['Red', 1]] }
    let(:expected_fuel_type_options) { [['Petrol', 1], ['Diesel', 2]] }
    let(:expected_transmission_options) { [['Manual', 1], ['Automatic', 2]] }
    let(:body_style_data) do
      [
        MPrimaryBodyStyle.new(name: 'Bus', primary_body_style_id: 1),
        MPrimaryBodyStyle.new(name: 'Convertible', primary_body_style_id: 2),
      ]
    end

    before do
      described_class.instance_variable_set(:@article_data, nil)
      allow(Search::MasterDataService).to receive(:load_maker_for_select).and_return([maker_options_data, model_options_data])
      allow(MasterInfo::MileageSearchOption).to receive(:pluck).and_return([%w[Low low], %w[High high]])
      allow(MasterInfo::AccidentSearchOption).to receive(:pluck).and_return([%w[Yes yes], %w[No no]])
      allow(MasterInfo::SteeringSearchOption).to receive(:pluck).and_return([%w[Left left], %w[Right right]])
      allow(MasterInfo::FobSearchOption).to receive(:pluck).and_return([['Option 1', 'option_1'], ['Option 2', 'option_2']])
      allow(MasterInfo::AnyDoorSearchOption).to receive(:pluck).and_return([['2 Doors', '2_doors'], ['4 Doors', '4_doors']])
      allow(MasterInfo::DriverTypeSearchOption).to receive(:pluck).and_return([%w[Manual manual], %w[Automatic automatic]])
      allow(MasterInfo::CapacitySearchOption).to receive(:pluck).and_return([%w[Small small], %w[Large large]])
      allow(MArticle).to receive(:where).with(group_name: %w[ColorType Transmission
                                                             FuelType]).and_return(double('MArticle', group_by: article_data))
      allow_any_instance_of(Search::MasterDataService).to receive(:send).with(:body_style_options).and_return([])
      allow_any_instance_of(Search::MasterDataService).to receive(:send).with(:secondary_body_style_options).and_return([])
      allow(car_counter_instance).to receive(:fetch_search_form_body_style).and_return(body_style_data)
    end

    it 'returns master data' do
      expected_data = {
        regis_year: expected_regis_year,
        regis_month: [[1, 1], [2, 2], [3, 3], [4, 4], [5, 5], [6, 6], [7, 7], [8, 8], [9, 9], [10, 10], [11, 11], [12, 12]],
        maker_options_data: maker_options_data,
        model_options_data: model_options_data,
        mileage_options: [%w[Low low], %w[High high]],
        accident_options: [%w[Yes yes], %w[No no]],
        steering_options: [%w[Left left], %w[Right right]],
        fob_options_data: [['Option 1', 'option_1'], ['Option 2', 'option_2']],
        any_door_options: [['2 Doors', '2_doors'], ['4 Doors', '4_doors']],
        driver_type_options: [%w[Manual manual], %w[Automatic automatic]],
        engine_capacity_options_data: [%w[Small small], %w[Large large]],
        color_options: expected_color_options,
        transmission_options: expected_transmission_options,
        fuel_type_options: expected_fuel_type_options,
        body_style_options: [['Bus', 1], ['Convertible', 2]],
        secondary_body_style_options: [['Aerial Platform', 1, 4], ['Aerial Platform', 10, 8], ['Asphalt Finisher', 2, 4],
                                       ['Car Carrier', 11, 8], ['Carrier', 3, 4], ['Concrete Pumping Truck', 12, 8], ['Crane', 13, 8],
                                       ['Dozer', 4, 4], ['Dump', 14, 8], ['Excavator', 5, 4], ['Flatbody', 15, 8], ['Forklift', 6, 4],
                                       ['Garbage Truck', 16, 8], ['Hook Lift', 17, 8], ['Mixer', 18, 8], ['Other', 9, 4], ['Other', 28, 8],
                                       ['Pickup', 29, 8], ['Refrigerated / Freezer', 19, 8], ['Roller', 7, 4], ['Sefl Loader / Safty Loader', 20, 8],
                                       ['Tanker', 21, 8], ['Tractor', 22, 8], ['Trailer / Trailer Set', 23, 8], ['Vaccum Truck', 24, 8],
                                       ['Van', 25, 8], ['Wheel Loader', 8, 4], ['Wing', 26, 8], ['Wrecker', 27, 8]]
      }

      expect(described_class.master_data(car_counter_instance)).to eq(expected_data)
    end
  end

  describe '#load_maker_for_select' do
    let(:car_counter_instance) { instance_double('CarCounter') }

    before do
      allow(car_counter_instance).to receive(:process_maker_data)
        .with(:alphabetical_make, limit_model_number: false)
        .and_return([
                      { name: 'Maker1', stock: 10, top_models: [{ name: 'Model1', stock: 5 }, { name: 'Model2', stock: 3 }] },
                      { name: 'Maker2', stock: 8, top_models: [{ name: 'Model3', stock: 7 }, { name: 'Model4', stock: 2 }] },
                    ])

      allow(car_counter_instance).to receive(:process_maker_data)
        .with(:popular_make, limit_model_number: false)
        .and_return([
                      { name: 'Maker3', stock: 15, top_models: [{ name: 'Model5', stock: 10 }, { name: 'Model6', stock: 5 }] },
                    ])
    end

    it 'returns maker options data and maker model options data' do
      maker_options_data, maker_model_options_data = described_class.load_maker_for_select(car_counter_instance)

      expect(maker_options_data).to eq([
                                         ['--- Popular ---', '', { disabled: 'disabled' }],
                                         ['Maker3 (15)', 'maker3'],
                                         ['--- Alphabetical ---', '', { disabled: 'disabled' }],
                                         ['Maker1 (10)', 'maker1'],
                                         ['Maker2 (8)', 'maker2'],
                                       ])

      expect(maker_model_options_data).to eq({
        'maker1' => [['Model1 (5)', 'model1'], ['Model2 (3)', 'model2']],
        'maker2' => [['Model3 (7)', 'model3'], ['Model4 (2)', 'model4']],
        'maker3' => [['Model5 (10)', 'model5'], ['Model6 (5)', 'model6']]
      }.to_json)
    end
  end
end
