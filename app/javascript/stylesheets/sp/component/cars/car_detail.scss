.fancybox__container {
  z-index: 999999;
  --fancybox-bg: #fff;
  --fancybox-color: #6a6969;
}

#car-carousel,
.f-thumbs.is-classic,
.fancybox__toolbar,
.fancybox__nav {
  --f-button-width: 40px;
  --f-button-height: 40px;
  --f-button-border-radius: 4px;

  --f-button-color: #0d0c22;
  --f-button-hover-color: var(--f-brand-color);

  --f-button-bg: #f1f5f9;
  --f-button-hover-bg: #e2e8f0;
  --f-button-active-bg: #e2e8f0;

  --f-button-svg-width: 20px;
  --f-button-svg-height: 20px;
  --f-button-svg-stroke-width: 2.25;
  --f-button-svg-filter: none;

  --f-button-svg-disabled-opacity: 0.2;
}

.f-carousel__slide {
  display: flex;
  align-items: center;
  justify-items: center;
}

.f-thumbs.is-classic {
  --f-thumb-width: 60px;
  --f-thumb-height: 55px;
  --f-thumb-gap: 4px;

  --f-thumb-opacity: 1;
  --f-thumb-selected-opacity: 0.5;

  --f-thumb-outline: 1px;
  --f-thumb-outline-color: var(--f-brand-color);

  --f-button-next-pos: 8px;
  --f-button-prev-pos: 8px;
}

.f-thumbs.is-classic.is-horizontal {
  padding: 0 56px;
}

.f-thumbs.is-classic.is-vertical {
  padding: 56px 0;
}

#productCarousel .f-carousel__nav {
  opacity: 0;
  transition: opacity 0.15s;
}

@media (hover: hover) {
  #productCarousel:hover .f-carousel__nav {
    opacity: 1;
  }
}

.fancybox__container {
  --fancybox-bg: #fff;
  --fancybox-color: #6a6969;

  --f-spinner-color-1: rgba(0, 0, 0, 0.1);
  --f-spinner-color-2: rgba(17, 24, 28, 0.8);
}

@media screen and (min-width: 640px) {
  .fancybox__container {
    flex-direction: row-reverse;
  }
}

.fancybox__thumbs.is-classic.is-horizontal {
  --f-thumb-width: 75px;
  --f-thumb-height: 55px;
  --f-thumb-gap: 8px;
}

.fancybox__thumbs.is-classic.is-vertical {
  --f-thumb-width: 100%;
  --f-thumb-height: 100px;

  width: 182px;

  outline: 1px solid #edeef0;
}

.fancybox__thumbs.is-classic.is-vertical .f-thumbs__track {
  padding: 0 16px;
}

.fancybox__toolbar {
  padding: 8px;
}

.fancybox__nav {
  --f-button-next-pos: 8px;
  --f-button-prev-pos: 8px;
}

.fancybox__carousel {
  min-width: 0;
  min-height: 0;
}

.fancybox__slide {
  padding: 8px;
}

.order__step-area {
  min-height: 57px;

  img {
    padding: 10px;
    border-radius: 20px;
  }
}

.support-link-block {
  .title {
    background: -webkit-linear-gradient(top,#f4f4f1 0%,$light-500 100%);
    color: $gray-100;
    border-top: 1px solid $gray-700;
    border-bottom: 1px solid $gray-700;
    padding: 0.6em 10px;
    font-size: 16px;
    font-weight: 700;
  }

  ul.link-list {
    li.each-item {
      display: inline-block;
      width: 100%;
      border-bottom: 1px $gray-700 solid;
      border-left: 1px $gray-700 solid;
      color: $gray-700;
      background: -webkit-linear-gradient(top,#edf1f5 0%,#fff 9%);

      a.item-link {
        padding: 10px;
        position: relative;
        display: flex;
        align-items: center;
        text-decoration: none;
        .content {
          color: #387bd0;
          font-weight: 700;
          margin-right: 5px;
        }

        span.ico.icon-chevron-right {
          position: absolute;
          top: 6px;
          right: 0px;
          font-weight: bold;
          font-size: 26px;
          color: $right-arrow-color;
        }
      }
    }
  }
}

.car-detail-wrapper {
  .title-block {
    background: -webkit-linear-gradient(top,$gray-800 0%,$light-500 100%);
    color: $gray-100;
    border-top: 1px solid $gray-700;
    border-bottom: 1px solid $gray-700;
    padding: 0.6em 15px;
    font-size: 16px;
    font-weight: bold;
    line-height: 1;
  }

  .title-detail {
    background: $light-900;
    border: 1px solid #b6b6b6;
    border-top: 1px solid white;
    border-left: none;
    border-bottom: none;
    text-align: center;
    font-size: 14px;
    padding: 0.6em;
    font-weight: 400;
  }

  .gallery-block {
    min-height: 290px;

    #car-carousel {
      img {
        width: 100%;
        margin: auto;
        object-fit: contain;
      }
      .number-block {
        position: absolute;
        border-radius: 2em;
        min-width: 50px;
        right: 10px;
        bottom: 10px;
        padding-top: 5px;
        padding-bottom: 5px;
        background-color: #9ba0ae;
        color: #fff;
        font-size: 10px;
        line-height: 1;
        text-align: center;
        box-sizing: border-box;
        z-index: 300;
      }
    }
  }

  .ask-price-block {
    padding: 20px 15px;
    position: relative;

    .best-price-link {
      background: #ff541a 0% 0% no-repeat padding-box;
      box-shadow: 0px 5px 0px #c84d23;
      border: none;
      border-radius: 8px;
      box-sizing: border-box;
      line-height: 0.7;
      text-shadow: -1px -1px 1px #00000033;
      width: 100%;
      height: 60px;
      display: block;
      overflow: hidden;
      text-align: center;
      white-space: nowrap;
      cursor: pointer;
      color: #fff;
      text-decoration: none;
      font-weight: bold;
      font-size: 24px;
      margin: 0 auto;

      .sub-text {
        font-size: 12px;
        font-family: $general-font;
        font-weight: normal;
      }

      .inquiry-tooltip {
        position: absolute;
        left: 50%;
        top: 11%;
        font-size: 12px;
        font-weight: 700;
        color: #000;
        text-shadow: none;
        background-color: #fff;
        border: 2px solid $orange;
        border-radius: 26px;
        width: 255px;
        height: 25px;
        line-height: 22px;
        text-align: center;
        animation-name: inquiry-tooltip-sp;
        animation-timing-function: ease-in-out;
        animation-duration: 2s;
        animation-iteration-count: infinite;
      }

      .inquiry-tooltip:after {
        content: "";
        position: absolute;
        bottom: -33%;
        left: 50%;
        height: 7px;
        width: 7px;
        background: #fff;
        box-sizing: border-box;
        transform: rotate(45deg) translate(-50%);
        border-bottom: inherit;
        border-right: inherit;
        box-shadow: inherit;
      }

      @keyframes inquiry-tooltip-sp {
        0%   {transform: translate(-50%, 0);}
        50%  {transform: translate(-50%, -7px);}
        100% {transform: translate(-50%, 0);}
      }
    }
  }

  .banner {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
  }

  .ask-price-bottom {
    position: fixed;
    width: 100%;
    bottom: -100px;
    transition: all .3s ease;
    -webkit-transition: all .3s ease;
    z-index: 99;

    .conseller {
      background: $orange;
      box-shadow: 0 4px 0 0 $dark-orange-100;
      border: none;
      border-radius: 5px;
      text-shadow: 0 -1px 0.5px $gray-100;
      color: #fff;
      font-weight: 700;
      width: 100%;

      a {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 58px;
        padding: 5px;
        text-align: center;
        text-decoration: none;

        .ask-price-text {
          cursor: pointer;
          color: #fff;
          font-weight: bold;
          font-size: 16px;

          .sub-text {
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }

    &.show {
      bottom: 0;
    }

    .inquiry-balloon {
      position: absolute;
      right: 20px;
      top: -42px;

      &::before {
        content: "";
        position: absolute;
        bottom: -16px;
        left: 11px;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        width: 8px;
        height: 8px;
        border: 8px solid transparent;
        border-top: 8px solid #000;
        opacity: 0.7;
      }

      .balloon-inner {
        border-radius: 4px;
        background: rgba(0, 0, 0, 0.7);
        padding: 10px 20px;
        color: #fff;

        .balloon-count {
          color: $dark-yellow;
          font-size: 18px;
        }
      }
    }
  }

  .favorite_buttons.custom_button {
    padding: 0 15px 15px;
  }

  .favorite_buttons{
    display: flex;
    padding: 15px;

    .favorite_button {
      background: #f4f3f1 0% 0% no-repeat padding-box;
      box-shadow: 0px 5px 0px #ddd;
      border-radius: 8px;
      width: 100%;
      height: 60px;
      border: none;
      font: normal normal bold 24px/60px Roboto;
      letter-spacing: 0px;
      color: #555;
      cursor: pointer;

      .favorite_icon {
        width: 25px;
        height: 25px;
        position: relative;
        top: 3px;
        display: inline-block;
        margin-right: 10px;
      }

      .favorite_icon_off {
        background: url(~images/star_icon_off.png) no-repeat;
      }

      .favorite_icon_on {
        background: url(~images/star_icon_on.png) no-repeat;
      }
    }

    .favorite__off,
    .favorite__on {
      &.inactive {
        display: none;
      }
    }
  }

  .specific-info-block {
    .alert-text {
      color: red;
    }

    table.specific-table {
      font-size: 14px;
      line-height: 1.3;
      color: $sm-content-color;
      border-collapse: collapse;
      border-spacing: 0;
      width: 100%;

      tr {
        display: table-row;
        td, th {
          padding: 0.6em;
          border: 1px solid #b6b6b6;
          word-break: break-word;
          text-align: left;
        }

        th {
          background: $gray-800;
          width: 36%;
          font-weight: normal;
          border-left: none;
        }

        td {
          border-right: none;

          .ask-best-price-button {
            background: $orange;
            box-shadow: 0 4px 0 0 $dark-orange-100;
            border: none;
            border-radius: 5px;
            text-shadow: 0 -.5px .5px $gray-100;
            color: #fff;
            padding: 10px 12px;
            font-size: 14px;
            text-decoration: none;
            vertical-align: middle;
          }
        }
      }
    }
  }

  .options-block {
    .ico {
      font-size: 12px;
    }

    .option-content {
      .each-block {
        .option-title {
          background-color: $gray-800;
          color: #707070;
          font-weight: 700;
          font-size: 12px;
          padding: 10px;
          border-top: 1px solid #b6b6b6;
          display: flex;
          align-items: center;
        }
        .option-block {
          display: none;
        }
        ul.option-list {
          max-height: 1000vh;
          height: auto;
          padding: 10px;
          display: grid;
          grid-template-columns: 33% auto 33%;
          column-gap: 5px;
          row-gap: 5px;

          li {
            background-color: $gray-800;
            font-weight: 400;
            color: #70705d;
            padding: 5px;
            text-align: center;
            font-size: 12px;
          }
          li.active {
            background-color: $light-500;
            font-weight: 700;
            color: #666259;
          }
        }
        &:first-child {
          .option-block {
            display: block;
          }
        }
      }
    }
  }
}

#first-car-image img {
  width: 100%;
  margin: auto;
  object-fit: contain;
}

@media screen and (orientation:landscape) {
  #car-carousel img, #first-car-image img {
    height: 240px;
  }
}

@media only screen and (max-width: 480px) {
  #car-carousel img, #first-car-image img {
    max-height: 450px;
  }
}

@media only screen and (min-width: 481px) and (max-width: 768px) {
  #car-carousel img, #first-car-image img {
    max-height: 600px;
  }
}
