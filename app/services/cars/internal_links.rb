module Cars
  class InternalLinks
    TOP_CAR = 5

    SECTIONS = {
      other_category: { title: 'Same Model x Years', link_info: :model_year_data },
      vehicle_stock: { title: 'Same Model x Prices', link_info: :model_price_data },
      body_style: { title: 'Same Make x BodyStyles', link_info: :make_body_style_data },
      popular_model: { title: 'Popular Models', link_info: :popular_model_data }
    }.freeze

    def initialize(make, model)
      @make_id = make.id_make
      @make_name_path = make.vc_name_e.downcase
      @model_id = model&.id_model
      @model_name_path = model&.vc_name_e&.downcase
      @solr_facet = Solr::FacetService.new
      @price_range = MasterInfo::InternalLink::PriceRange.all
      @body_style = MasterInfo::InternalLink::BodyStyle.all
      @popular_model = Search::GetPopularModelCarsService.new(make, model)
    end

    def call
      Rails.cache.fetch("internal_links_detail_#{@make_name_path}_#{@model_name_path}", expires_in: 1.hour) do
        SECTIONS.filter_map do |_, section|
          link_info = send(section[:link_info])
          next if link_info.blank?

          { condition: link_info, title: section[:title] }
        end
      end
    end

    private

    def fetch_condition_counts(facet_queries)
      response = @solr_facet.call_facet(queries: facet_queries).dig('facet_counts', 'facet_queries')
      response&.values || []
    end

    def facet_queries_price
      @price_range.map { |condition| "Price:#{condition.value} AND MakeID:#{@make_id} AND ModelID:#{@model_id}" }
    end

    def facet_queries_body_style
      @body_style.map { |condition| "BodyStyle1:#{condition.id} AND MakeID:#{@make_id}" }
    end

    def build_condition_data(collection, counts, &block)
      collection.zip(counts).filter_map do |item, count|
        next if count.zero?

        [block.call(item), count]
      end
    end

    def model_year_data
      data = @solr_facet.call_facet(
        group_field_name: 'ModelYear',
        query: "MakeID:#{@make_id} AND ModelID:#{@model_id}",
      )
      facet_counts = data.dig('facet_counts', 'facet_pivot', 'ModelYear')&.sort_by { |item| -item['value'].to_i }
      return [] if facet_counts.blank?

      facet_counts.map do |item|
        year = item['value']
        count = item['count']
        [["used_car/#{@make_name_path}/#{escape_param(@model_name_path)}/?fid=#{year}&jid=#{year}", "#{year} (#{count})"], count]
      end
    end

    def model_price_data
      build_condition_data(@price_range, fetch_condition_counts(facet_queries_price)) do |condition|
        ["used_car/#{@make_name_path}/#{escape_param(@model_name_path)}/?#{condition.query}", condition.text]
      end
    end

    def make_body_style_data
      build_condition_data(@body_style, fetch_condition_counts(facet_queries_body_style)) do |condition|
        ["used_car/#{@make_name_path}/all/?#{condition.query}", condition.text]
      end
    end

    def popular_model_data
      models = @popular_model.list(TOP_CAR)
      return [] if models.blank?

      models.map do |model|
        [
          ["used_car/#{model.maker_nm.downcase}/#{escape_param(model.model_nm.downcase)}/", "#{model.maker_nm} #{model.model_nm}"],
          1,
        ]
      end
    end

    def escape_param(value)
      CGI.escape(value).gsub('+', '%20')
    end
  end
end
