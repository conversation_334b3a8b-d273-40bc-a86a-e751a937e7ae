module Search
  class InternalLinkCategory
    JAPAN_MAKER = [
      { maker_id: 1, maker_name: 'Toyota' },
      { maker_id: 2, maker_name: 'Nissan' },
      { maker_id: 3, maker_name: 'Honda' },
      { maker_id: 5, maker_name: 'Mazda' },
      { maker_id: 4, maker_name: 'Mitsubishi' },
    ].freeze

    SECTIONS = {
      maker: :handle_maker,
      body_style: :handle_body_style,
      price: :handle_price
    }.freeze

    def initialize(category_param, category_value)
      @category_data = MasterInfo::InternalLink::OtherCategory.find_by_params({ category_param => category_value })
      @category_name = @category_data&.text
      @body_style = MasterInfo::InternalLink::BodyStyle.all
      @solr_facet = Solr::FacetService.new
      @price_range = MasterInfo::InternalLink::PriceRange.all
    end

    def call
      return unless @category_data.present?

      Rails.cache.fetch("internal_links_category_#{@category_name}", expires_in: 1.hour) do
        SECTIONS.filter_map do |_, handler|
          link_info = send(handler)
          next if link_info.blank? || link_info[:condition].blank?

          link_info
        end
      end
    end

    private

    def handle_maker
      condition = build_condition_data(JAPAN_MAKER, 'maker') do |popular_make|
        ["used_car/#{popular_make[:maker_name].downcase}/all/?#{@category_data.query}",
         "#{popular_make[:maker_name]} x #{@category_name}"]
      end
      { title: "Make x #{@category_name}", condition: condition }
    end

    def handle_body_style
      condition = build_condition_data(@body_style, 'body_style') do |body_style|
        ["used_car/all/all/?#{body_style[:query]}&#{@category_data.query}", "#{body_style[:text]} x #{@category_name}"]
      end
      { title: "BodyStyles x #{@category_name}", condition: condition }
    end

    def handle_price
      condition = build_condition_data(@price_range, 'price') do |price_range|
        ["used_car/all/all/?#{price_range[:query]}&#{@category_data.query}", "#{price_range[:text]} x #{@category_name}"]
      end
      { title: "Car Price(FOB) x #{@category_name}", condition: condition }
    end

    def fetch_condition_counts
      @all_facet_counts ||= begin
        queries = combined_facet_queries
        result = @solr_facet.call_facet(queries: queries.values).dig('facet_counts', 'facet_queries') || {}
        queries.keys.zip(result.values).to_h
      end
    end

    def combined_facet_queries
      queries = []

      @price_range.each_with_index do |p, i|
        queries << ["price_#{i}", "Price:#{p[:value]} AND #{solr_query_for_category}"]
      end

      JAPAN_MAKER.each_with_index do |m, i|
        queries << ["maker_#{i}", "MakeID:#{m[:maker_id]} AND #{solr_query_for_category}"]
      end

      @body_style.each_with_index do |bs, i|
        queries << ["body_style_#{i}", "BodyStyle1:#{bs[:id]} AND #{solr_query_for_category}"]
      end

      queries.to_h
    end

    def solr_query_for_category
      @category_data&.solr_query || ''
    end

    def build_condition_data(collection, condition_type, &block)
      collection.each_with_index.filter_map do |item, index|
        count = fetch_condition_counts["#{condition_type}_#{index}"] || 0
        next if count.zero?

        [block.call(item), count]
      end
    end
  end
end
