doctype html
html lang='en'
  head
    = render 'shared/datalayer'
    = render 'shared/header_gtm' if Rails.env.production?
    = render 'shared/resource_hints'
    = display_meta_tags
    = yield(:serp_thumbnail)
    = csrf_meta_tags
    = csp_meta_tag
    - unless request_from_smart_phone?
      meta[name="viewport" content="width=device-width,initial-scale=1"]
    = render 'shared/metas_structured_data'
    = favicon_pack_tag 'favicon.ico'
    = javascript_pack_tag get_pack_name('pc'), 'data-turbolinks-track': 'reload', defer: true
    = stylesheet_pack_tag get_pack_name('pc'), media: 'all', 'data-turbolinks-track': 'reload'
    = yield(:js_pack_custom)
    script async=true src='https://www.google.com/recaptcha/api.js'
  body [
    class=append_classes_if(nil, 'viewpc--force', condition: !smart_phone?)
    data-environment="#{Rails.env}" data-logged="#{user_signed_in?}"
  ]
    = render 'shared/body_gtm' if Rails.env.production?
    .wrapper__pc id='wrapper'
      = render 'shared/set_size_image_cls'
      = render 'shared/scroll_to_top_btn'
      = render 'shared/grecaptcha_callback'
      = render 'shared/environment_info'
      = render 'shared/gdpr'
      = render 'shared/header'
      = render 'shared/gnav'
      = render 'shared/live_chat'
      = render 'shared/affiliate_popup'
      = render 'shared/overlay_loading'
      main.main-area
        = yield
      = render "#{ @footer || 'shared/footer' }"
