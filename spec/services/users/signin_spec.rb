require 'rails_helper'

RSpec.describe Users::Signin, type: :service do
  describe '#call' do
    context 'when the credentials are correct' do
      it 'returns the user' do
        response_body = { 'UserID' => '123456', 'UserName' => 'test_user', 'UserHandleName' => 'test_user',
                          'UserUpdateDateTime' => '2022%2f12%2f07+16%3a08%3a22', 'UserLogonDate' => '2024%2f01%2f05+14%3a52%3a26',
                          'Hash' => '33F8C25D54B1B2BB35BAAFC4581A31BC', 'Message' => 'success' }
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send)
          .and_return(instance_double(TcvCoreApi::Response, success?: true, body: response_body))

        result = described_class.exec(response_body['UserName'], 'test_password')
        expect(result).to eq(response_body)
      end
    end

    context 'when the credentials are incorrect' do
      it 'returns nil' do
        allow_any_instance_of(TcvCoreApi::Request).to receive(:send).and_return(instance_double(TcvCoreApi::Response, success?: false))

        result = described_class.exec('test_user', 'wrong password')
        expect(result).to be_nil
      end
    end

    context 'when the credentials are incorrect' do
      it 'returns nil' do
        result = described_class.exec('test_user', 'wrong password')
        response_body = { 'Hash' => '', 'Message' => 'Failed Login', 'UserHandleName' => '', 'UserID' => 0,
                          'UserLogonDate' => '', 'UserName' => '', 'UserUpdateDateTime' => '' }
        expect(result).to eq(response_body)
      end
    end
  end
end
