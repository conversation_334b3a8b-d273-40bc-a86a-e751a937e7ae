class CarStockPresenter < BasePresenter
  include CarPriceRate
  include ActionView::Helpers::NumberHelper

  attr_reader :car

  UNKNOWN_MILEAGE = 85
  CHANGED_ODOMETER = 86

  def initialize(car_data, current_currency, master_make = nil, master_model = nil)
    @car = car_data.transform_keys(&:to_sym)
    @master_make = master_make
    @master_model = master_model
    @current_currency = current_currency
    super()
  end

  def car_detail_tags
    [mileage, transmission, accident(parentheses: false), fuel, displacement]
  end

  def show_market_price?
    car[:marketPriceStatus].to_i.nonzero?
  end

  def generate_page_title
    "#{make_name} #{model_name} #{model_year}"
  end

  def name
    "#{model_year} #{make_name} #{model_name}"
  end

  def name_sp
    main = "Used&nbsp; #{make_name}&nbsp; #{model_name}&nbsp; #{model_year}&nbsp; #{car[:trimName]}&nbsp; (#{simple_mileage})"
    "#{main}&nbsp; #{'Accident' if car[:isAccident]}".html_safe
  end

  def car_img_thumbs
    view_context.content_tag(:div, car_img_thumbs_handler(width: '700px', height: '525px'),
                             class: 'slide__gallery--js image__gallery car__detail-pic-area')
  end

  def chassis_no
    "#{car[:chassisNo][0..-4]}***"
  end

  def model_code
    validate_value(car[:modelCode])
  end

  def registration_time
    return model_year.presence || '-' unless car[:firstRegistrationDate]

    view_context.l(car[:firstRegistrationDate]&.to_date, format: :short)
  end

  def manufacture_time(is_sp: false)
    return is_sp ? '-' : 'Confirm with the Seller' if car[:manufactureYear].nil? || car[:manufactureYear].zero?

    return car[:manufactureYear] if car[:manufactureMonth].nil? || car[:manufactureMonth].zero?

    view_context.l("#{car[:manufactureYear]}/#{car[:manufactureMonth]}".to_date, format: :short)
  end

  def mileage
    return '-' unless car[:odmeter]

    suffixes =  case car[:odometerOption]
                when UNKNOWN_MILEAGE
                  'Unknown Mileage'
                when CHANGED_ODOMETER
                  'Changed Odometer'
                end

    "#{simple_mileage} #{suffixes}"
  end

  def simple_mileage
    "#{view_context.number_with_delimiter(car[:odmeter])} km"
  end

  def transmission
    @transmission ||= get_value_from_article('Transmission', car[:transmissionId])
  end

  def accident(parentheses: true)
    return beauty_accident_text('Accident Not Repaired', parentheses) if car[:isAccident] == true
    return beauty_accident_text('No Accident', parentheses) if car[:vehicleOption]&.include?(NO_ACCIDENT_HISTORY_ID)

    parentheses ? '' : '-'
  end

  def displacement
    car[:displacement].to_i.zero? ? '-' : "#{number_with_delimiter(car[:displacement])}cc"
  end

  def fuel
    @fuel ||= get_value_from_article('FuelType', car[:fuelId])
  end

  def body_style1
    @body_style1 ||= MPrimaryBodyStyle.find_by(primary_body_style_id: car[:bodystyle1Id])&.name
  end

  def body_style2
    MSecondaryBodyStyle.find_by(secondary_body_style_id: car[:bodystyle2Id])&.name || '-'
  end

  def steering
    @steering ||= get_value_from_article('Steering', car[:steeringId])
  end

  def exterior_color
    @exterior_color ||= get_value_from_article('ColorType', car[:exteriorColorId])
  end

  def interior_color
    get_value_from_article('ColorType', car[:interiorColorId]) || '-'
  end

  def drive_type
    @drive_type ||= get_value_from_article('DriveType', car[:driveTypeId])
  end

  def door
    @door ||= get_value_from_article('Doors', car[:door])
  end

  def seats_amount
    car[:passengers]
  end

  def dimension
    return '-' if [car[:vehicleHeight], car[:vehicleLength], car[:vehicleWidth]].any?(&:zero?)

    length = (car[:vehicleLength] / 10).ceil
    width  = (car[:vehicleWidth]  / 10).ceil
    height = (car[:vehicleHeight] / 10).ceil
    volumetric = ((length * width * height).to_f / 1_000_000).round(2)
    volumetric = 0 if volumetric.zero?

    "#{length}cm×#{width}cm×#{height}cm=#{volumetric}m³"
  end

  def condition
    !car[:isNew] || car[:isNew].zero? ? 'Used' : 'New'
  end

  def user_number
    return "#{car[:sellerId]}-#{car[:userNumber]}" if car[:sellerId].present?

    car[:userNumber] || '-'
  end

  def mechanical_problem
    car[:mechanicalProblem].presence || '-'
  end

  def comment
    validate_value(car[:detail]).html_safe
  end

  def expiry_date
    car[:terminateDate] ? "#{view_context.l(car[:terminateDate]&.to_date, format: :jst_default)} (JST)" : '-'
  end

  def model_year
    car[:modelYear]
  end

  def master_make
    @master_make ||= MasterMake.find_by(id_make: car[:makeId])
  end

  def make_name
    @make_name ||= master_make&.vc_name_e
  end

  def master_model
    @master_model ||= MasterModel.find_by(id_model: car[:modelId], id_make: car[:makeId])
  end

  def model_name
    @model_name ||= master_model&.vc_name_e
  end

  def option?(option_id)
    option_ids = car[:vehicleOption]
    option_ids.include?(option_id)
  end

  def img_urls
    return @img_urls if @img_urls

    @img_urls = []
    japan_car    = car[:pegasusUserCountryNumber].to_i == JP_COUNTRY_ID ? '/japan%20car' : ''
    model_string = "/#{[name.split].flatten.map(&:downcase).join('+')}"
    base_url     = "#{Settings.cdn.img_url}#{car[:sellerId]}/#{car[:itemId]}#{japan_car}#{model_string}/"
    (1..car[:imageCount]).each do |image_number|
      image_number = image_number.to_s.rjust(2, '0')
      @img_urls << ["#{base_url}#{image_number}.jpg", "#{base_url}#{image_number}s.jpg"]
    end
    @img_urls
  end

  def main_photo
    first_car = img_urls&.first
    first_car ? "#{first_car[0]}?height=270&width=360&type=resize" : view_context.asset_pack_path('media/images/nophoto.gif')
  end

  def car_thumb_url(**options)
    url = img_urls&.first&.first
    return '' unless url

    url += "?width=#{options[:width]}&height=#{options[:height]}&type=#{options[:type]}" if options[:width] && options[:height]
    url
  end

  def display_fob_price(exchange_rate_target: @current_currency)
    fob_price(car[:price], car[:priceExchangeRateId], exchange_rates(car[:priceExchangeRateId], exchange_rate_target), exchange_rate_target)
  end

  def display_bnpl_price(exchange_rate_target: @current_currency)
    price = car[:price].to_i / 2
    fob_price(price, car[:priceExchangeRateId], exchange_rates(car[:priceExchangeRateId], exchange_rate_target), exchange_rate_target)
  end

  private

  def validate_value(value)
    value.presence || '-'
  end

  def article_data
    @article_data ||= MArticle.where(group_name: %w[FuelType Steering ColorType DriveType Doors Transmission])
  end

  def get_value_from_article(group_name, id)
    article_data.detect { |item| item.group_name == group_name && item.article_id == id }&.en
  end

  def car_img_thumbs_handler(**options)
    onerror = { onerror: "this.src='#{view_context.asset_pack_path('media/images/nophoto.gif')}'" }
    main_img_url  = img_urls[0][0].sub('01.jpg', '01w.jpg')
    main_img_wrap = view_context.content_tag(:img, nil, { src: main_img_url, loading: 'eager', alt: name, **onerror,
                                                          class: 'mainpic slide__gallery-mainPic--js' }.merge(options))
    prev_btn = view_context.content_tag(:div, nil, class: 'image__gallery-prev ico icon-chevron-left-bold slide__gallery-prev--js')
    next_btn = view_context.content_tag(:div, nil, class: 'image__gallery-next ico icon-chevron-right-bold slide__gallery-next--js')
    image_gallery_mainpic = view_context.content_tag(:div, main_img_wrap, class: 'image__gallery-mainpic-block', fetchpriority: 'high')
    image_gallery_mainpic + prev_btn + next_btn + thumb_wrapper(img_urls, width: '60px', height: '45px', **onerror)
  end

  def thumb_wrapper(img_urls, **options)
    view_context.content_tag(:ul, class: 'image__gallery-thumb-list used__pic-thumb-list slide__gallery-Listitem--js') do
      img_urls.collect.with_index(1) do |url, index|
        sub_img = view_context.content_tag(:img, nil, { src: url.last, loading: 'lazy', 'data-main-src': url.first, alt: name }.merge(options))
        view_context.content_tag(:li, sub_img, { class: 'image__gallery-thumb-item used__thumb-item slide__gallery-thumbItem--js',
                                                 'data-thumb-no': index })
      end.join.html_safe
    end
  end

  def beauty_accident_text(input, parentheses)
    parentheses ? "(#{input})" : input
  end
end
