FROM ruby:3.2.2-alpine AS builder

ENV NODE_OPTIONS=--openssl-legacy-provider
ARG BUILD_PACKAGES="build-base curl-dev ruby-dev libxml2-dev libxslt-dev"
ARG DEV_PACKAGES="yaml-dev zlib-dev mysql-dev nodejs yarn curl"
ARG RUBY_PACKAGES="tzdata ruby-json yaml libc6-compat"

RUN apk add --update \
  $BUILD_PACKAGES \
  $DEV_PACKAGES \
  $RUBY_PACKAGES

ENV RAILS_ENV=development
ENV NODE_ENV=development
ENV RACK_ENV=development
ENV MALLOC_ARENA_MAX=2
ENV NOKOGIRI_USE_SYSTEM_LIBRARIES=1

RUN gem install bundler -v 2.4.10 --no-document

WORKDIR /webapp

# Copy the application codes
COPY . /webapp/

RUN bundle install --jobs=4

COPY package.json yarn.lock /webapp/
RUN yarn install --check-files
