# syntax=docker/dockerfile:1
FROM ruby:3.2.2-alpine AS builder
ENV BUNDLER_VERSION=2.4.10
ARG PORT=8080
ENV PORT=${PORT}

ENV LANG=C.UTF-8
ENV TZ=Asia/Tokyo
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

ENV NODE_OPTIONS=--openssl-legacy-provider

ARG BUILD_PACKAGES="build-base curl-dev ruby-dev libxml2-dev libxslt-dev"
ARG DEV_PACKAGES="yaml-dev zlib-dev mysql-dev nodejs yarn curl"
ARG RUBY_PACKAGES="tzdata ruby-json yaml libc6-compat"

RUN apk add --update \
  $BUILD_PACKAGES \
  $DEV_PACKAGES \
  $RUBY_PACKAGES

ARG RAILS_ENV=production

ENV RAILS_ENV=${RAILS_ENV}
ENV NODE_ENV=${RAILS_ENV}
ENV RACK_ENV=${RAILS_ENV}
ENV RAILS_LOG_TO_STDOUT=true
ENV RAILS_SERVE_STATIC_FILES=true
ENV MALLOC_ARENA_MAX=2
ENV NOKOGIRI_USE_SYSTEM_LIBRARIES=1

ARG SECRET_KEY_BASE
ENV SECRET_KEY_BASE=${SECRET_KEY_BASE}

ARG PRIMARY_DB_USERNAME
ENV PRIMARY_DB_USERNAME=${PRIMARY_DB_USERNAME}

ARG PRIMARY_DB_PASSWORD
ENV PRIMARY_DB_PASSWORD=${PRIMARY_DB_PASSWORD}

ARG PRIMARY_DB_NAME
ENV PRIMARY_DB_NAME=${PRIMARY_DB_NAME}

ARG PRIMARY_DB_SOCKET
ENV PRIMARY_DB_SOCKET=${PRIMARY_DB_SOCKET}

ARG GCP_PROJECT_ID
ENV GCP_PROJECT_ID=${GCP_PROJECT_ID}

ARG REGION
ENV REGION=${REGION}

ARG GCP_BATCH_HOST
ENV GCP_BATCH_HOST=${GCP_BATCH_HOST}

ARG GCP_TASK_QUEUE
ENV GCP_TASK_QUEUE=${GCP_TASK_QUEUE}

ARG GCP_TASK_SERVICE_EMAIL
ENV GCP_TASK_SERVICE_EMAIL=${GCP_TASK_SERVICE_EMAIL}

ARG REDIS_PORT
ENV REDIS_PORT=${REDIS_PORT}

ARG REDIS_HOST
ENV REDIS_HOST=${REDIS_HOST}

ARG REDIS_AUTH_STRING
ENV REDIS_AUTH_STRING=${REDIS_AUTH_STRING}

ARG REDIS_CACHE_URL
ENV REDIS_CACHE_URL=${REDIS_CACHE_URL}

ARG REDIS_SESSION_URL
ENV REDIS_SESSION_URL=${REDIS_SESSION_URL}

ARG SPLIT_USERNAME
ENV SPLIT_USERNAME=${SPLIT_USERNAME}

ARG SPLIT_PASSWORD
ENV SPLIT_PASSWORD=${SPLIT_PASSWORD}

ARG NEWRELIC_MONITOR_MODE_ENABLED=false
ENV NEWRELIC_MONITOR_MODE_ENABLED=${NEWRELIC_MONITOR_MODE_ENABLED}

ARG NEWRELIC_LICENSE_KEY
ENV NEWRELIC_LICENSE_KEY=${NEWRELIC_LICENSE_KEY}

ARG NEWRELIC_APP_NAME
ENV NEWRELIC_APP_NAME=${NEWRELIC_APP_NAME}

RUN mkdir /app
WORKDIR /app
COPY . /app/

RUN gem install bundler -v ${BUNDLER_VERSION} --no-document

RUN bundle install --jobs=4 --without development test

RUN yarn install --check-files

# Precompile bootsnap code for faster boot times
RUN bundle exec bootsnap precompile --gemfile app/ lib/

# Precompiling assets for production without requiring secret RAILS_MASTER_KEY
RUN SECRET_KEY_BASE=${SECRET_KEY_BASE} bundle exec rails assets:precompile

COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh
ENTRYPOINT ["docker-entrypoint.sh"]

# Configure the main process to run when running the image
CMD ["rails", "server", "-b", "0.0.0.0"]
