doctype html
html lang='en'
  head
    = render 'shared/datalayer'
    = render 'shared/header_gtm' if Rails.env.production?
    = render 'shared/resource_hints'
    meta[name="viewport" content="width=device-width,initial-scale=1"]
    = render 'shared/meta_theme_color'
    = display_meta_tags
    = yield(:serp_thumbnail)
    = csrf_meta_tags
    = csp_meta_tag
    = render 'shared/metas_structured_data'
    = favicon_pack_tag 'favicon.ico'
    = javascript_pack_tag get_pack_name('sp'), 'data-turbolinks-track': 'reload', defer: true
    = stylesheet_pack_tag get_pack_name('sp'), media: 'all', 'data-turbolinks-track': 'reload'
    = yield(:js_pack_custom)
  body data-environment="#{Rails.env}" data-logged="#{user_signed_in?}"
    = render 'shared/body_gtm' if Rails.env.production?
    .wrapper__sp
      = render 'shared/set_size_image_cls'
      = render 'sp/shared/environment_info'
      = render 'shared/gdpr'
      = render 'shared/affiliate_popup'
      = render 'sp/shared/header'
      = render 'shared/overlay_loading'
      = yield
      = render "#{ @footer || 'sp/shared/footer' }"
