import { Carousel, Fancybox } from '@fancyapps/ui/'
import { Thumbs } from '@fancyapps/ui/dist/carousel/carousel.thumbs.esm.js'
import '@fancyapps/ui/dist/carousel/carousel.css'
import '@fancyapps/ui/dist/fancybox/fancybox.css'
import '@fancyapps/ui/dist/carousel/carousel.thumbs.css'

(function() {
  $(document).ready(function() {
    init()
  })

  var init = function() {
    generateMainSlider()
    viewDetailPhoto()
    showHideOption()
  }

  var generateMainSlider = function() {
    if (!document.getElementById('car-carousel')) return

    new Carousel(
      document.getElementById('car-carousel'),
      {
        infinite: true,
        Dots: false,
        Thumbs: {
          type: 'classic',
          Carousel: {
            slidesPerPage: 1,
            Navigation: true,
            center: true,
            fill: true,
            dragFree: true,
          },
        },
        on: {
          ready: (instance) => {
            const pages = instance.slides.length
            $('#car-carousel .number-block').text(`1 / ${pages}`)
            setTimeout(() => {
              $('#first-car-image').addClass('d-none')
              $('#car-carousel').removeClass('d-none')
            }, 50)
          },
          change: (instance) => {
            // Current page
            const page = instance.page
            // Page count
            const pages = instance.pages.length
            $('#car-carousel .number-block').text(`${page + 1} / ${pages}`)
          }
        }
      },
      { Thumbs },
    )
  }

  var viewDetailPhoto = function() {
    Fancybox.bind("[data-fancybox='gallery']", {
      idle: false,
      compact: false,
      dragToClose: false,

      animated: false,
      showClass: 'f-fadeSlowIn',
      hideClass: false,

      Carousel: {
        infinite: true,
      },

      Images: {
        zoom: false,
        Panzoom: {
          maxScale: 1.5,
        },
      },

      Toolbar: {
        absolute: true,
        display: {
          left: ['infobar'],
          middle: [],
          right: ['close'],
        },
      },

      Thumbs: {
        type: 'classic',
        Carousel: {
          axis: 'x',

          slidesPerPage: 1,
          Navigation: true,
          center: true,
          fill: true,
          dragFree: true,

          breakpoints: {
            '(min-width: 640px)': {
              axis: 'y',
            },
          },
        },
      },
    })
  }
  var showHideOption = function() {
    $('.car-detail-wrapper .options-block .option-title').on('click', function() {
      const bottomBlock = $(this).parent().find('.option-block')
      if ((bottomBlock).is(':hidden')) {
        bottomBlock.slideDown('fast')
      } else {
        bottomBlock.slideUp('fast')
      }
    })
  }
}).call(this)
