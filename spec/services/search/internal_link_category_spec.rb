require 'rails_helper'

RSpec.describe Search::InternalLinkCategory do
  describe '#call' do
    let(:category_param) { 'st' }
    let(:category_value) { '13' }
    let(:solr_facet_service) { instance_double(Solr::FacetService) }

    subject { described_class.new(category_param, category_value) }

    before do
      body_styles = [
        double('BodyStyle', id: 7, text: 'SUV', query: 'bsty=7'),
        double('BodyStyle', id: 6, text: 'Sedan', query: 'bsty=6'),
        double('BodyStyle', id: 1, text: 'Hatchback', query: 'bsty=1'),
      ]

      body_styles.each do |bs|
        allow(bs).to receive(:[]).with(:id).and_return(bs.id)
        allow(bs).to receive(:[]).with(:text).and_return(bs.text)
        allow(bs).to receive(:[]).with(:query).and_return(bs.query)
      end

      allow(MasterInfo::InternalLink::BodyStyle).to receive(:all).and_return(body_styles)

      price_ranges = [
        double('PriceRange', value: '[0 TO 500]', text: 'Under US$500', query: 'prct=500', id: 1),
        double('PriceRange', value: '[500 TO 1000]', text: 'US$500-1,000', query: 'prcf=500&prct=1000', id: 2),
        double('PriceRange', value: '[1000 TO 1500]', text: 'US$1,000-1,500', query: 'prcf=1000&prct=1500', id: 3),
      ]

      price_ranges.each do |pr|
        allow(pr).to receive(:[]).with(:value).and_return(pr.value)
        allow(pr).to receive(:[]).with(:text).and_return(pr.text)
        allow(pr).to receive(:[]).with(:query).and_return(pr.query)
        allow(pr).to receive(:[]).with(:id).and_return(pr.id)
      end

      allow(MasterInfo::InternalLink::PriceRange).to receive(:all).and_return(price_ranges)

      mock_category = double('Category', text: 'Left Hand Drive', solr_query: 'SteeringID:13', query: 'st=13')
      allow(MasterInfo::InternalLink::OtherCategory).to receive(:find_by_params).and_return(mock_category)

      allow(Solr::FacetService).to receive(:new).and_return(solr_facet_service)
      allow(solr_facet_service).to receive(:call_facet).with(
        queries: [
          'Price:[0 TO 500] AND SteeringID:13',
          'Price:[500 TO 1000] AND SteeringID:13',
          'Price:[1000 TO 1500] AND SteeringID:13',
          'MakeID:1 AND SteeringID:13',
          'MakeID:2 AND SteeringID:13',
          'MakeID:3 AND SteeringID:13',
          'MakeID:5 AND SteeringID:13',
          'MakeID:4 AND SteeringID:13',
          'BodyStyle1:7 AND SteeringID:13',
          'BodyStyle1:6 AND SteeringID:13',
          'BodyStyle1:1 AND SteeringID:13',
        ],
      ).and_return({
                     'facet_counts' => {
                       'facet_queries' => {
                         'Price:[0 TO 500] AND SteeringID:13' => 0,
                         'Price:[500 TO 1000] AND SteeringID:13' => 7,
                         'Price:[1000 TO 1500] AND SteeringID:13' => 9,
                         'MakeID:1 AND SteeringID:13' => 15,
                         'MakeID:2 AND SteeringID:13' => 10,
                         'MakeID:3 AND SteeringID:13' => 0,
                         'MakeID:5 AND SteeringID:13' => 5,
                         'MakeID:4 AND SteeringID:13' => 0,
                         'BodyStyle1:7 AND SteeringID:13' => 12,
                         'BodyStyle1:6 AND SteeringID:13' => 8,
                         'BodyStyle1:1 AND SteeringID:13' => 0
                       }
                     }
                   })
    end

    context 'when category is Left Hand Drive (st=13)' do
      it 'returns list of internal links with make recommendations' do
        result = subject.call
        maker_section = result.find { |r| r[:title] == 'Make x Left Hand Drive' }

        expect(maker_section).not_to be_nil
        expect(maker_section[:condition].count).to eq(3)
        expect(maker_section[:condition][0][0][0]).to eq('used_car/toyota/all/?st=13')
        expect(maker_section[:condition][0][0][1]).to eq('Toyota x Left Hand Drive')
        expect(maker_section[:condition][1][0][0]).to eq('used_car/nissan/all/?st=13')
        expect(maker_section[:condition][1][0][1]).to eq('Nissan x Left Hand Drive')
      end

      it 'returns list of internal links with body style recommendations' do
        result = subject.call
        body_style_section = result.find { |r| r[:title] == 'BodyStyles x Left Hand Drive' }

        expect(body_style_section).not_to be_nil
        expect(body_style_section[:condition].count).to eq(2)
        expect(body_style_section[:condition][0][0][0]).to eq('used_car/all/all/?bsty=7&st=13')
        expect(body_style_section[:condition][0][0][1]).to eq('SUV x Left Hand Drive')
        expect(body_style_section[:condition][1][0][0]).to eq('used_car/all/all/?bsty=6&st=13')
        expect(body_style_section[:condition][1][0][1]).to eq('Sedan x Left Hand Drive')
      end

      it 'returns list of internal links with price range recommendations' do
        result = subject.call
        price_section = result.find { |r| r[:title] == 'Car Price(FOB) x Left Hand Drive' }

        expect(price_section).not_to be_nil
        expect(price_section[:condition].count).to eq(2)
        expect(price_section[:condition][0][0][0]).to eq('used_car/all/all/?prcf=500&prct=1000&st=13')
        expect(price_section[:condition][0][0][1]).to eq('US$500-1,000 x Left Hand Drive')
        expect(price_section[:condition][1][0][0]).to eq('used_car/all/all/?prcf=1000&prct=1500&st=13')
        expect(price_section[:condition][1][0][1]).to eq('US$1,000-1,500 x Left Hand Drive')
      end
    end

    context 'when category is Manual (tmns=6)' do
      let(:category_param) { 'tmns' }
      let(:category_value) { '6' }

      before do
        mock_category = double('Category', text: 'Manual', solr_query: 'TransmissionID:6', query: 'tmns=6')
        allow(MasterInfo::InternalLink::OtherCategory).to receive(:find_by_params).and_return(mock_category)

        body_styles = [
          double('BodyStyle', id: 7, text: 'SUV', query: 'bsty=7'),
          double('BodyStyle', id: 6, text: 'Sedan', query: 'bsty=6'),
          double('BodyStyle', id: 1, text: 'Hatchback', query: 'bsty=1'),
        ]

        body_styles.each do |bs|
          allow(bs).to receive(:[]).with(:id).and_return(bs.id)
          allow(bs).to receive(:[]).with(:text).and_return(bs.text)
          allow(bs).to receive(:[]).with(:query).and_return(bs.query)
        end

        allow(MasterInfo::InternalLink::BodyStyle).to receive(:all).and_return(body_styles)

        price_ranges = [
          double('PriceRange', value: '[0 TO 500]', text: 'Under US$500', query: 'prct=500', id: 1),
          double('PriceRange', value: '[500 TO 1000]', text: 'US$500-1,000', query: 'prcf=500&prct=1000', id: 2),
          double('PriceRange', value: '[1000 TO 1500]', text: 'US$1,000-1,500', query: 'prcf=1000&prct=1500', id: 3),
        ]

        price_ranges.each do |pr|
          allow(pr).to receive(:[]).with(:value).and_return(pr.value)
          allow(pr).to receive(:[]).with(:text).and_return(pr.text)
          allow(pr).to receive(:[]).with(:query).and_return(pr.query)
          allow(pr).to receive(:[]).with(:id).and_return(pr.id)
        end

        allow(MasterInfo::InternalLink::PriceRange).to receive(:all).and_return(price_ranges)

        allow(solr_facet_service).to receive(:call_facet).with(
          queries: [
            'Price:[0 TO 500] AND TransmissionID:6',
            'Price:[500 TO 1000] AND TransmissionID:6',
            'Price:[1000 TO 1500] AND TransmissionID:6',
            'MakeID:1 AND TransmissionID:6',
            'MakeID:2 AND TransmissionID:6',
            'MakeID:3 AND TransmissionID:6',
            'MakeID:5 AND TransmissionID:6',
            'MakeID:4 AND TransmissionID:6',
            'BodyStyle1:7 AND TransmissionID:6',
            'BodyStyle1:6 AND TransmissionID:6',
            'BodyStyle1:1 AND TransmissionID:6',
          ],
        ).and_return({
                       'facet_counts' => {
                         'facet_queries' => {
                           'Price:[0 TO 500] AND TransmissionID:6' => 5,
                           'Price:[500 TO 1000] AND TransmissionID:6' => 15,
                           'Price:[1000 TO 1500] AND TransmissionID:6' => 10,
                           'MakeID:1 AND TransmissionID:6' => 20,
                           'MakeID:2 AND TransmissionID:6' => 15,
                           'MakeID:3 AND TransmissionID:6' => 10,
                           'MakeID:5 AND TransmissionID:6' => 0,
                           'MakeID:4 AND TransmissionID:6' => 5,
                           'BodyStyle1:7 AND TransmissionID:6' => 18,
                           'BodyStyle1:6 AND TransmissionID:6' => 12,
                           'BodyStyle1:1 AND TransmissionID:6' => 7
                         }
                       }
                     })
      end

      it 'returns list of internal links with make recommendations for Manual' do
        result = subject.call
        maker_section = result.find { |r| r[:title] == 'Make x Manual' }

        expect(maker_section).not_to be_nil
        expect(maker_section[:condition].count).to eq(4)
        expect(maker_section[:condition][0][0][0]).to eq('used_car/toyota/all/?tmns=6')
        expect(maker_section[:condition][0][0][1]).to eq('Toyota x Manual')
      end

      it 'returns list of internal links with body style recommendations for Manual' do
        result = subject.call
        body_style_section = result.find { |r| r[:title] == 'BodyStyles x Manual' }

        expect(body_style_section).not_to be_nil
        expect(body_style_section[:condition].count).to eq(3)
        expect(body_style_section[:condition][0][0][0]).to eq('used_car/all/all/?bsty=7&tmns=6')
        expect(body_style_section[:condition][0][0][1]).to eq('SUV x Manual')
      end

      it 'returns list of internal links with price range recommendations for Manual' do
        result = subject.call
        price_section = result.find { |r| r[:title] == 'Car Price(FOB) x Manual' }

        expect(price_section).not_to be_nil
        expect(price_section[:condition].count).to eq(3)
        expect(price_section[:condition][0][0][0]).to eq('used_car/all/all/?prct=500&tmns=6')
        expect(price_section[:condition][0][0][1]).to eq('Under US$500 x Manual')
      end
    end

    context 'when the category is not valid' do
      let(:category_param) { nil }
      let(:category_value) { nil }

      before do
        allow(MasterInfo::InternalLink::OtherCategory).to receive(:find_by_params).and_return(nil)
      end

      it 'returns nil' do
        expect(subject.call).to be_nil
      end
    end
  end
end
