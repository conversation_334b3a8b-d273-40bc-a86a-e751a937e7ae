section#modal__popup--assist {
  position: fixed;
  z-index: 100001;
  display: none;
  width: 500px;
  color: $gray-200;
  top: 50%;
  left: 50%;
  opacity: 1;
  text-align: center;
  text-decoration: none;
  background-color: $gray-800;
  border: 4px solid #f3c52c;
  border-radius: 8px;
  -webkit-transition: all .3s ease;
  -o-transition: all .3s ease;
  transition: all .3s ease;
  transform: translate(-50%, -50%) translate3d(0,-10px,0);
  box-shadow: 0 0 13px 0 $gray-100;
  -webkit-transform: translate(-50%, -50%) translate3d(0,-10px,0);
  -webkit-box-shadow: 0 0 13px 0 $gray-100;

  button.icon-close {
    position: fixed;
    top: 10px;
    right: 10px;
    width: 36px;
    height: 36px;
    padding: 0;
    font-size: 24px;
    border-radius: 50%;
    background-color: #dacb8d;
    color: #fff;
    cursor: pointer;
    border: 0;
  }

  .popup__main__content {
    background-color: #fbeeb9;
    padding: 20px;
    border-radius: 8px;

    .popup__title {
      font-size: 20px;
      font-weight: bold;
    }

    .popup__text {
      font-size: 18px;
      padding: 15px;
      text-align: left;
      line-height: 1.5;
    }

    .popup__button {
      padding: 15px;

      a.btn.popup-btn-phone {
        width: 100%;
        height: 50px;
        background-color: $orange;
        text-shadow: 0 -1px 0.5px $dark-orange-200;
        box-shadow: 0 4px 0 0 $dark-orange-100;
        &:hover { opacity: .7; }
        padding: 2px 0.8em;
        font-weight: 700;
        font-size: 24px;
      }

      a.popup--not-padding {
        padding: 0 !important;
      }
    }
  }
}
