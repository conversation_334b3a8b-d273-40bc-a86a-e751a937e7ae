class PromotionsController < ApplicationController
  before_action :require_xhr_request, only: [:check_affiliate]

  def check_affiliate
    is_show_show_affiliate = whitelist_path? && check_condition

    cookies[:affiliate] = { value: 'affiliate', expires: Time.current.end_of_day } if is_show_show_affiliate

    render json: { show_affiliate: is_show_show_affiliate }
  end

  private

  def check_condition
    return false if cookies[:affiliate].present?

    if current_user.present?
      country_has_ability_affiliate? && current_user['userServices'].exclude?(USER_SELLER_CODE)
    else
      country_has_ability_affiliate?
    end
  end

  def whitelist_path?
    return false if params[:current_path].nil?

    params[:current_path] == '/' || params[:current_path][0, 10] == '/used_car/'
  end
end
