.campaign {
  &__container {
    background-color: #facc15;
    min-height: 100vh;
    padding: 30px 20px;
  }

  &__section {
    margin-bottom: 1.5rem;
  }

  &__section.last {
    margin-bottom: 0;
  }

  &__description {
    background-color: #000;
    color: #45a0c3;
    padding: 1rem 1.5rem;

    &-paragraph {
      margin-bottom: 0.5rem;
      padding-left: 10px;
      font-weight: bold;
      position: relative;

      &:before {
        content: "";
        display: inline-block;
        transform: rotate(45deg);
        height: 10px;
        width: 5px;
        border-bottom: 4px solid #fc032c;
        border-right: 4px solid #fc032c;
        margin-right: 10px;
        position: absolute;
        left: -5px;
      }
    }
  }

  &__celebration {
    background-color: white;
    color: #45a0c3;
    padding: 1rem 1.5rem;

    &-title {
      font-weight: bold;
      font-size: 1.5rem;
      margin-bottom: 0.5rem;
      color: #ff0027;
      position: relative;
      padding-left: 10px;

      &:before {
        content: "";
        display: inline-block;
        height: calc(100% + 10px);
        width: 10px;
        background-color: #ff0027;
        margin-right: 10px;
        position: absolute;
        left: -10px;
      }
    }

    &-paragraph {
      margin-bottom: 0.5rem;
      font-weight: bold;
      color: black;
      position: relative;
      padding-left: 10px;

      &:before {
        content: "";
        display: inline-block;
        height: calc(100% + 10px);
        width: 10px;
        background-color: black;
        margin-right: 10px;
        position: absolute;
        left: -10px;
      }
    }
  }

  &__giveaway {
    background-color: #fef08a;

    &-title {
      font-weight: bold;
      margin-bottom: 0.5rem;
      background-color: #f8bf03;
      color: white;
      display: inline-block;
      padding: 4px 10px;
    }

    &-highlight {
      color: #dc2626;
    }

    &-info {
      padding: 1rem 1.5rem;
    }

    &-subtitle {
      font-weight: bold;
      margin-bottom: 0.5rem;
      color: white;
      display: inline-block;
      padding: 4px 10px;
      background-color: #4aace3;
    }

    &-gift {
      position: relative;
      margin-left: 24px;
      font-weight: bold;

      &:before {
        position: absolute;
        left: -24px;
        content: "";
        width: 20px;
        height: 20px;
        background-color: #4aace3;
        border-radius: 50%;
      }
    }

    &-list {
      list-style-type: none;
      margin-left: 1.5rem;
      margin-bottom: 1rem;

      &-item {
        margin-bottom: 0.5rem;
        position: relative;
        font-weight: bold;

        &:after {
          position: absolute;
          left: -24px;
          content: "";
          width: 20px;
          height: 20px;
          background-color: #fed952;
          border-radius: 50%;
        }
      }
    }

    &-note {
      margin-bottom: 1rem;
      margin-left: 18px;
      position: relative;
      font-size: 12px;

      img.icon-asterisk {
        width: 10px;
        height: 10px;
        position: absolute;
        left: -13px;
        top: 2px;
      }
    }
  }

  &__term {
    background-color: white;
    padding: 1rem 1.5rem;


    &-bold {
      font-weight: bold;
      font-size: 16px;
      position: relative;
      margin-left: 24px;

      &:before {
        content: "";
        display: inline-block;
        height: 100%;
        width: 10px;
        background-color: black;
        margin-right: 10px;
        position: absolute;
        left: -24px;
      }
    }

    &-condition {
      border-top: 1px solid black;
      border-bottom: 1px solid black;
      margin: 10px 0;
      padding: 4px;
      display: inline-block;
    }

    ol {
      font-size: 12px;
      padding-left: 14px;

      li {
        padding: 3px 0;
      }
    }

    &-small-text {
      font-size: 0.75rem;

      &-title {
        font-weight: bold;
      }
    }
  }
}

h3.subject__change {
  background-color: #f86315;
  color: white;
  width: 100%;
  text-align: center;
  padding: 5px 0;
  position: relative;

  &:before {
    content: "";
    display: inline-block;
    height: 100%;
    width: 10px;
    background-color: black;
    margin-right: 10px;
    position: absolute;
    left: 0;
    top: 0;
  }
}
