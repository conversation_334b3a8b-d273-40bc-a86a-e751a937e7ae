.d-block {
  display: block!important
}

.d-inline-block {
  display: inline-block!important
}

.d-flex {
  display: flex!important;
}

.d-none {
  display: none!important;
}

.m-0 {
  margin: 0!important;
}

.ms-0 {
  margin-left: 0!important;
}

.mt-0 {
  margin-top: 0!important;
}

.p-0 {
  padding: 0!important;
}

.ms-2 {
  margin-left: 2px!important;
}

.me-2 {
  margin-right: 2px!important;
}

.mt-2 {
  margin-top: 2px!important;
}

.ms-3 {
  margin-left: 3px!important;
}

.mx-4 {
  margin-right: 4px!important;
}

.ms-4, .mx-4 {
  margin-left: 4px!important;
}

.me-4 {
  margin-right: 4px!important;
}

.mb-4 {
  margin-bottom: 4px!important;
}

.mt-4 {
  margin-top: 4px!important;
}

.mb-5 {
  margin-bottom: 5px!important;
}

.pe-5 {
  padding-right: 5px!important;
}

.me-6 {
  margin-right: 6px!important;
}

.pt-6 {
  padding-top: 6px!important;
}

.mb-10 {
  margin-bottom: 10px!important;
}

.mt-10 {
  margin-top: 10px!important;
}

.pt-10 {
  padding-top: 10px!important;
}

.ms-12 {
  margin-left: 12px!important;
}

.mt-12 {
  margin-top: 12px!important;
}

.me-14 {
  margin-right: 14px!important;
}

.mb-14 {
  margin-bottom: 14px!important;
}

.ms-16 {
  margin-left: 16px!important;
}

.mt-16 {
  margin-top: 16px!important;
}

.ms-18 {
  margin-left: 18px!important;
}

.ms-24 {
  margin-left: 24px!important;
}

.me-24 {
  margin-right: 24px!important;
}

.pe-240 {
  padding-right: 240px;
}

.position-relative {
  position: relative!important;
}

.position-absolute {
  position: absolute!important;
}

.translateY-1 {
  transform: translateY(1px);
}

.fw-bold {
  font-weight: 700!important;
}

.fs-12 {
  font-size: 12px!important;
}

.fs-13 {
  font-size: 13px!important;
}

.fs-14 {
  font-size: 14px!important;
}

.fs-16 {
  font-size: 16px!important;
}

.fs-20 {
  font-size: 20px!important;
}

.fs-32 {
  font-size: 32px!important;
}

.text-center {
  text-align: center;
}

.align-left {
  text-align: left;
}

.align-items-center {
  align-items: center;
}

.justify-space-between {
  justify-content: space-between;
}

.both { clear: both; }

.container, .container-dynamic {
  width: 1260px;
  margin: 0 auto;
}

.group_btn--hide {
  display: none;

  &.active { display: block; }
}
