require 'rails_helper'

RSpec.describe Search::InternalLinkPriceRange do
  let(:price_params) { { prcf: 1_000, prct: 1_500 } }
  let(:price_range_query) { price_params.to_query }
  let(:price_range) { { query: price_range_query, text: 'US$1,000-1,500', value: '[1000 TO 1500]', master_info_id: 3 } }
  let(:solr_facet_service) { instance_double(Solr::FacetService) }
  let(:popular_service) { instance_double(Search::GetPopularForAllMakeService) }
  let(:body_styles) { [{ id: 6, url: 'used_car/all/all/?bsty=6', text: 'Sedan', query: 'bsty=6' }] }
  let(:options) { [{ text: 'Sunroof', url: 'used_car/all/all/?op=33', query: 'op=33', solr_query: 'Options:33' }] }
  let(:popular_models) { [OpenStruct.new(maker_id: 1, model_id: 101, maker_nm: 'Toyota', model_nm: 'Corolla')] } # rubocop:disable Style/OpenStructUse

  subject(:service) { described_class.new(price_params) }

  before do
    allow(MasterInfo::InternalLink::PriceRange).to receive(:find_by).with(query: price_range_query).and_return(price_range)
    allow(Solr::FacetService).to receive(:new).and_return(solr_facet_service)
    allow(MasterInfo::InternalLink::BodyStyle).to receive(:all).and_return(body_styles)
    allow(MasterInfo::InternalLink::OtherCategory).to receive(:all).and_return(options)
    allow(Search::GetPopularForAllMakeService).to receive(:new).and_return(popular_service)
    allow(Rails.cache).to receive(:fetch).and_yield
  end

  describe '#initialize' do
    it 'sets up instance variables correctly' do
      expect(service.instance_variable_get(:@price_range_query)).to eq(price_params.to_query)
      expect(service.instance_variable_get(:@solr_facet)).to eq(solr_facet_service)
      expect(service.instance_variable_get(:@body_style)).to eq(body_styles)
      expect(service.instance_variable_get(:@option)).to eq(options)
      expect(service.instance_variable_get(:@popular_for_all_make_service)).to eq(popular_service)
    end
  end

  describe '#call' do
    context 'when price range is not found' do
      before do
        allow(MasterInfo::InternalLink::PriceRange).to receive(:find_by).and_return(nil)
      end

      it 'returns nil' do
        expect(service.call).to be_nil
      end
    end

    context 'when price range is found' do
      let(:facet_counts) do
        {
          'maker_0' => 10,
          'maker_1' => 5,
          'maker_2' => 3,
          'maker_3' => 7,
          'maker_4' => 1,
          'body_style_0' => 5,
          'option_0' => 3,
          'model_0' => 7
        }
      end

      before do
        allow(solr_facet_service).to receive(:call_facet).and_return('facet_counts' => { 'facet_queries' => facet_counts })
        allow(popular_service).to receive(:list_all_model_in_price_range).with(3).and_return(popular_models)
      end

      it 'caches the result' do
        expect(Rails.cache).to receive(:fetch).with("internal_links_price_range_#{price_range_query}", expires_in: 1.hour)
        service.call
      end

      it 'returns formatted link info for all sections' do
        result = service.call
        expect(result).to include(
          hash_including(
            title: 'Make x US$1,000-1,500',
            condition: [
              [["used_car/toyota/all/?#{price_range_query}", 'Toyota x US$1,000-1,500'], 10],
              [["used_car/nissan/all/?#{price_range_query}", 'Nissan x US$1,000-1,500'], 5],
              [["used_car/honda/all/?#{price_range_query}", 'Honda x US$1,000-1,500'], 3],
              [["used_car/mazda/all/?#{price_range_query}", 'Mazda x US$1,000-1,500'], 7],
              [["used_car/mitsubishi/all/?#{price_range_query}", 'Mitsubishi x US$1,000-1,500'], 1],
            ],
          ),
          hash_including(
            title: 'BodyStyles x US$1,000-1,500',
            condition: [[["used_car/all/all/?#{price_range_query}&bsty=6", 'Sedan x US$1,000-1,500'], 5]],
          ),
          hash_including(
            title: 'US$1,000-1,500 x Option',
            condition: [[["used_car/all/all/?#{price_range_query}&op=33", 'US$1,000-1,500 x Sunroof'], 3]],
          ),
          hash_including(
            title: 'Popular Models',
            condition: [[["used_car/toyota/corolla/?#{price_range_query}", 'Toyota Corolla'], 7]],
          ),
        )
      end
    end
  end

  describe 'private methods' do
    describe '#handle_japan_maker' do
      before do
        allow(service).to receive(:fetch_condition_counts).and_return('maker_0' => 10)
      end

      it 'builds japan maker conditions' do
        result = service.send(:handle_japan_maker)
        expect(result).to eq(
          title: 'Make x US$1,000-1,500',
          condition: [[["used_car/toyota/all/?#{price_range_query}", 'Toyota x US$1,000-1,500'], 10]],
        )
      end
    end

    describe '#handle_body_style' do
      before do
        allow(service).to receive(:fetch_condition_counts).and_return('body_style_0' => 5)
      end

      it 'builds body style conditions' do
        result = service.send(:handle_body_style)
        expect(result).to eq(
          title: 'BodyStyles x US$1,000-1,500',
          condition: [[["used_car/all/all/?#{price_range_query}&bsty=6", 'Sedan x US$1,000-1,500'], 5]],
        )
      end
    end

    describe '#handle_option' do
      before do
        allow(service).to receive(:fetch_condition_counts).and_return('option_0' => 3)
      end

      it 'builds option conditions' do
        result = service.send(:handle_option)
        expect(result).to eq(
          title: 'US$1,000-1,500 x Option',
          condition: [[["used_car/all/all/?#{price_range_query}&op=33", 'US$1,000-1,500 x Sunroof'], 3]],
        )
      end
    end

    describe '#handle_popular_model' do
      before do
        allow(popular_service).to receive(:list_all_model_in_price_range).and_return(popular_models)
        allow(service).to receive(:fetch_condition_counts).and_return('model_0' => 7)
      end

      it 'builds popular model conditions' do
        result = service.send(:handle_popular_model)
        expect(result).to eq(
          title: 'Popular Models',
          condition: [[["used_car/toyota/corolla/?#{price_range_query}", 'Toyota Corolla'], 7]],
        )
      end
    end
  end
end
