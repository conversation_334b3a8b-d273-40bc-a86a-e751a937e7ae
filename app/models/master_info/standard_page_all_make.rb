module MasterInfo
  class StandardPageAllMake < Base
    PRICE_RANGES = [
      { value: 1, from: 0, to: 500 },
      { value: 2, from: 500, to: 1000 },
      { value: 3, from: 1000, to: 1500 },
      { value: 4, from: 1500, to: 2000 },
      { value: 5, from: 2000, to: 2500 },
      { value: 6, from: 2500, to: 5000 },
      { value: 7, from: 5000, to: 10_000 },
      { value: 8, from: 10_000, to: 20_000 },
      { value: 9, from: 20_000, to: 0 },
    ].freeze

    def self.find_price_range(prcf, prct)
      return if prcf.nil? && prct.nil?

      price_range = PRICE_RANGES.find { |range| range[:from] == prcf.to_i && range[:to] == prct.to_i }
      return unless price_range

      find_by(param_name: 'prc', param_value: price_range[:value])
    end

    def self.find_category_param_and_value(params)
      where(type: 'Option').find do |c|
        params[c.param_name].present? && params[c.param_name] == c.param_value.to_s
      end
    end
  end
end
