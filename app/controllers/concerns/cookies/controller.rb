module Cookies
  module Controller
    extend ActiveSupport::Concern

    private

    def set_cookies(name:, value:, domain: Settings.tcv_short_domain, expire: default_expire_time)
      final_value =
        if domain.present?
          "#{name}=#{value}; domain=#{domain}; expires=#{datetime_formater(expire)}; path=/"
        else
          "#{name}=#{value}; expires=#{datetime_formater(expire)}; path=/"
        end
      response_cookies = response.header['Set-Cookie']
      response.header['Set-Cookie'] = response_cookies.presence ? Array(response_cookies).push(final_value) : final_value
    end

    def default_expire_time
      Settings.cookies.expiration_time.days.from_now
    end

    def datetime_formater(datetime)
      datetime&.httpdate
    end

    def sign_value_tcv_cookies(expect_key, expect_value)
      raise CookiesMissingValueError if cookies['tcvcookies'].blank?

      cookies['tcvcookies'].split('&').map do |obj|
        key = obj.split('=').first
        next obj if key != expect_key

        "#{key}=#{expect_value}"
      end.join('&')
    rescue
      cookies.delete('tcvcookies')
    end

    def value_of(str, expect_key)
      return unless str

      str.split('&').detect do |obj|
        key, value = obj.split('=')
        return value if key == expect_key
      end
    end
  end
end

class CookiesMissingValueError < StandardError; end
