require 'rails_helper'

PASSWORD = 'Password@123'.freeze
RSpec.describe UsersController, type: :request do
  characters = [('a'..'z'), ('0'..'9')].map(&:to_a).flatten
  random_string = (0...7).map { characters[rand(characters.length)] }.join
  email = "#{random_string}@gmail.com"

  describe '#signup' do
    it 'creates a new user and renders JSON' do
      allow(Users::Signup).to receive_message_chain(:new, :create).and_return({ email: email })

      params = { email: email, password: PASSWORD, country: '4', country_code: '0', number: '123123123123', purpose: '3',
                 recaptcha_response: 'test_recaptcha', controller: 'users', action: 'signup' }
      post users_signup_path, params: params
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)).to include('user' => { 'email' => email })
    end
  end

  describe '#signin' do
    it 'signs in user and renders JSO<PERSON>' do
      allow(Users::Signin).to receive(:exec).and_return({ email: email })
      post users_signin_path, params: { email: email, password: PASSWORD, remember_me: false }
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)).to include('user' => { 'email' => email })
    end

    it 'renders JSON indicating user signed in' do
      allow(Users::Signin).to receive(:exec).and_return({ 'email' => email })
      post users_signin_path, params: { email: email, password: PASSWORD, remember_me: false }
      post users_user_signed_in_path

      expect(response).to have_http_status(:ok)
    end
  end

  describe '#shortcutmember' do
    it 'creates shortcut member and renders JSON' do
      allow(Users::ShortcutMember).to receive_message_chain(:new, :create).and_return(result: 'success')
      cookies['CVPD'] = 'some_value'
      get users_shortcutmember_path
      expect(response).to have_http_status(:ok)
      expect(JSON.parse(response.body)).to include('result' => { 'result' => 'success' })
    end
  end
end
