class SearchController < ApplicationController
  include Metas::Search
  include SearchBreadcrumbs
  include ActionView::Helpers::NumberHelper

  before_action :redirect_correct_query_params, only: :index
  before_action :prepare_data, :tcv_user_authentication!, :favorites_merge_fgkey, only: %i[index advanced_search]
  before_action :set_breadcrumbs, :search_service, :add_favorite_callback, only: :index
  before_action :require_valid_page_param, only: %i[same_model popular_models]
  before_action :require_xhr_request, only: %i[same_model popular_models
                                               favorite_cars description fetch_partial_user_review inquiries_cars
                                               change_sort_condition update_per_page fetch_stock_count
                                               fetch_export_performance_rate fetch_export_performance_rate_query
                                               fetch_stock_count_query]

  helper_method :dest_sort_by_total_price, :enable_paginate?, :contain_additional_search_params?
  helper_method :valid_page_param?

  def index
    start_ab_test_sort_by_featured

    @data_search_country_code = params.permit(:co)[:co]
    @checkbox_price_options = ::Price::EstimateTotal::Options.new(current_est_location[:country]&.number).all if ship_estimated?
    reset_tradetpopts(@checkbox_price_options) if @checkbox_price_options
    @cars, @car_count, @cars_pr_count = ::Search::Cars.new(
      **search_condition(current_per_page, current_country[:country_code]),
    ).exec(no_pr: tdk_search_object.vehicle_stock_not_kenya?)
    sort_conditions = dest_sort_by_total_price.present? ? MasterInfo::SortCondition.all : MasterInfo::SortCondition.where.not(value: [60, 61])
    @current_country_code = user_country_code

    if smart_phone?
      @sort_conditions = sort_conditions.group_by(&:group)
      @full_text_condition_search = full_text_condition_search
      @request_desired_vehicles_link = UrlGenerator::RequestDesiredVehicles.new(@car_count, search_service.queries,
                                                                                !user_signed_in?, helpers.buyer_user?).exec
    else
      @sort_conditions = sort_conditions.map do |condition|
        [condition.value == Cookies::SortCondition::SORT_FEATURED_CODE ? condition.name : "#{condition.group} - #{condition.name}", condition.value]
      end
      prepare_search_data
    end

    prepare_data_for_cars(@cars, additional_params: true)
    @list_country_can_shipestimate = Rails.cache.fetch('list_country_can_shipestimate', expires_in: 3.hours) do
      MCountry.list_country_can_shipestimate.pluck(:country, :number).uniq.sort
    end

    pn_param = params[:pn].to_i
    @paginatable_array = Kaminari.paginate_array(@cars, total_count: @car_count).page(pn_param + 1).per(current_per_page - @cars_pr_count)
    total_pages = @paginatable_array.total_pages

    @valid_page_param =
      if params[:pn].present?
        params[:pn].to_s == pn_param.to_s && pn_param >= 0 && pn_param <= (total_pages - 1)
      else
        true
      end

    is_valid = @valid_page_param && valid_params?

    handle_status_404 unless is_valid

    @render_chart_with_query_data = @current_price_range_data || @current_standard_param_data if only_price_range_params? || only_one_standard_param?

    prepare_data_internal_links

    @page_num = pn_param.zero? ? '' : "(#{pn_param + 1})"

    @h1_content = h1_content

    car_make_or_model = @car_model&.vc_name_e || @car_make&.vc_name_e
    @graph_title = car_make_or_model || @render_chart_with_query_data&.[](:text).to_s

    prepare_template
  end

  def advanced_search
    @h1_top_content = h1_top_content
    @hidden_structured_data = true

    prepare_search_data
    set_template_for_advanced_search
  end

  def search_counter
    render json: { count: number_with_delimiter(search_service.count) }
  end

  def search_redirect
    current_sort_condition # reset sort type
    destination_url = Search::ParamsService.new(params, session).redirect_data
    redirect_to destination_url
  end

  def change_sort_condition
    sign_sort_condition(params.permit(:ptid)[:ptid].to_i)
    head :ok
  end

  def update_per_page
    sign_per_page(params.permit(:per)[:per].to_i)
    head :ok
  end

  def same_model
    return head :bad_request if !make_model_valid? || contain_additional_search_params?

    @cars = same_model_service(@car_make, @car_model).similar_models
    prepare_data_for_cars(@cars)
    template = smart_phone? ? 'sp/search/same_models/block' : 'search/same_models/block'

    render partial: template, layout: false, status: :ok
  end

  def popular_models
    return head :bad_request if !make_model_valid? || contain_additional_search_params?

    @popular_models = popular_models_service.call
    prepare_data_for_cars(@popular_models)
    template = smart_phone? ? 'sp/search/popular_models/block' : 'search/popular_models/block'

    render partial: template, layout: false, status: :ok
  end

  def favorite_cars
    car_ids = begin
      JSON.parse(params[:car_ids])
    rescue
      []
    end

    favorite_car_ids = fetch_favorites(car_ids).uniq

    render json: { favorite_car_ids: favorite_car_ids }, status: :ok
  end

  def description
    return head :bad_request unless make_valid?

    description_file = File.read('public/make_model_description.xml')
    file_content = Nokogiri::XML(description_file)
    xpath = ->(id_make, id_model) { "//data[@name='MakeModel_#{id_make}_#{id_model}']//value" }
    description = ->(file, path) { file.xpath(path).children.first&.text }
    @catalog_description = description.call(file_content, xpath.call(@car_make.id_make, @car_model.id_model)) if model_valid?
    @catalog_title = @car_make.vc_name_e

    if @catalog_description.present?
      @catalog_title = "#{@car_make.vc_name_e} #{@car_model.vc_name_e}"
    else
      @catalog_description = description.call(file_content, xpath.call(@car_make.id_make, 0))
    end

    template = smart_phone? ? 'sp/search/description' : 'search/description'

    render partial: template, layout: false, status: :ok
  end

  def fetch_partial_user_review
    return render json: { template: nil }, status: :no_content unless make_model_valid?

    @user_reviews = Rails.cache.fetch("search_user_review_#{@car_make.id_make}_#{@car_model.id_model}", expires_in: 3.hours) do
      Search::UserReviewsService.new(@car_make, @car_model).call
    end
    return render json: { template: nil }, status: :no_content unless @user_reviews

    partial = smart_phone? ? 'sp/search/user_reviews' : 'search/user_reviews'
    render partial: partial, layout: false, status: :ok
  end

  def inquiries_cars
    car_ids = begin
      JSON.parse(params[:car_ids])
    rescue
      []
    end

    inquiries_counter = Inquiries::OfferCount.new(car_ids, cookies['CVPD']).exec
    inquiries_counter = inquiries_counter.filter { |_, v| v.to_i.positive? }

    render json: { inquiries_counter: inquiries_counter }, status: :ok
  end

  def fetch_modals
    return redirect_to root_path unless request.xhr?

    @country_list = Rails.cache.fetch('prepare_search_data_country_list', expires_in: 1.hour) do
      MCountry.country_for_select_options.pluck(:number, :country, :country_code)
    end

    @current_country_code = user_country_code

    render json: {
      modal_signin: render_to_string(partial: 'search/modals/modal_signin', layout: false).html_safe,
      modal_signup: render_to_string(partial: 'search/modals/modal_signup', layout: false).html_safe,
      modal_contact_seller: render_to_string(partial: 'search/modals/modal_contact_seller', layout: false).html_safe
    }, status: :ok
  end

  def fetch_export_performance_rate
    fetch_data_by_class(Charts::ExportPerformanceRate)
  end

  def fetch_stock_count
    fetch_data_by_class(Charts::StockCount)
  end

  def fetch_export_performance_rate_query
    fetch_data_with_query_by_class(Charts::ExportPerformanceRateQuery)
  end

  def fetch_stock_count_query
    fetch_data_with_query_by_class(Charts::StockCountQuery)
  end

  private

  def fetch_data_by_class(subclass)
    return head :bad_request unless make_valid?

    model_id = @car_model.id_model if model_valid?
    result = subclass.new(@car_make.id, model_id).build_data
    render json: result
  end

  def fetch_data_with_query_by_class(subclass)
    return head :bad_request unless params[:param_name] && params[:param_value]

    result = subclass.new(params[:param_name], params[:param_value]).build_data

    render json: result
  end

  def prepare_search_data
    @country_has_regular_car = Rails.cache.fetch("prepare_search_data_regular_car_for_#{current_country[:country_code]}", expires_in: 1.hour) do
      MCountry.country_has_regular_car(current_country[:country_code])&.a3 || false
    end
  end

  def search_service
    @search_service ||= Search::SearchConditionFilter.new(params, current_country[:country_code])
  end

  def same_model_service(make, model)
    same_model_params = {
      current_user: current_user,
      car_make: make,
      car_model: model,
      current_currency: current_currency,
      user_country_code: user_country_code
    }

    @same_model_service ||= Search::SameModelService.new(**same_model_params)
  end

  def set_template_for_advanced_search
    @hide_action_in_footer = true
    render(template: 'sp/search/advanced_search', layout: 'sp_application')
  end

  def search_condition(per_page, country_code = user_country_code)
    colr_options = sorting_by_total_price? ? { core_number: dest_sort_by_total_price[:core_number] } : {}
    {
      params: params, current_currency: current_currency,
      user_country_code: user_country_code, access_country_code: country_code,
      sort_condition: current_sort_condition,
      cookies: cookies, per_page: per_page,
      experiment: @sort_by_featured
    }.merge(colr_options)
  end

  def dest_sort_by_total_price
    return {} unless ship_estimated?

    country_name = current_est_location[:country]&.number
    port_id      = current_est_location[:port_id]&.to_i
    ::DESTINATION_HAS_SORT_BY_TOTAL_PRICE.detect { |des| des[:country_number] == country_name && des[:port_id] == port_id } || {}
  end

  def contain_additional_search_params?(rejected_params = nil)
    MasterInfo::SearchKeyMappingSolr.where(key: params.keys).reject { |param| param.key == rejected_params.to_s }.present?
  end

  def only_price_range_params?
    return false if make_valid? || model_valid?

    return false if MasterInfo::SearchKeyMappingSolr.where(key: params.keys).reject { |param| %w[prcf prct].include?(param.key) }.present?

    (@current_price_range_data ||= MasterInfo::StandardPageAllMake.find_price_range(params[:prcf], params[:prct])).present?
  end

  def only_one_standard_param?
    return false if make_valid? || model_valid?

    current_params = MasterInfo::SearchKeyMappingSolr.where(key: params.keys)

    return false unless current_params.size == 1

    param_key = current_params.first.key
    param_value = params[param_key]
    return false if param_value.include?('*') || param_value.include?("\u0000")

    standardize_param_value = param_value.to_i == param_value.to_d ? param_value.to_i : param_value
    (@current_standard_param_data ||= MasterInfo::StandardPageAllMake.find_by(param_name: param_key, param_value: standardize_param_value)).present?
  end

  def find_category_data
    category_data = MasterInfo::StandardPageAllMake.find_category_param_and_value(params)
    return unless category_data.present?
    return if contain_additional_search_params?(category_data.param_name.to_sym)

    {
      param_name: category_data.param_name,
      param_value: category_data.param_value.to_s
    }
  end

  def prepare_data_for_cars(cars, additional_params: false)
    @car_ids = cars.map { |car| car['ItemID'] }
    @favorite_url_callback = request.base_url + (additional_params ? Search::ParamsService.new(params, session).redirect_data : search_path)
  end

  def enable_paginate?
    @car_count > current_per_page
  end

  def popular_models_service
    @popular_models_service ||= Search::GetPopularModelCarsService.new(@car_make, @car_model, current_currency, user_country_code)
  end

  def recommend_make_model_service
    @recommend_make_model_service ||= Search::RecommendMakeModel.new(@car_make.id_make, user_country_code, smart_phone?)
  end

  def valid_page_param?
    ActiveModel::Type::Boolean.new.cast(params[:valid_page_param])
  end

  def require_valid_page_param
    head :bad_request unless valid_page_param?
  end

  def start_ab_test_sort_by_featured
    ab_test(:sort_by_featured) do |sort|
      cookies['abt-v2'] = { value: sort, expires: 30.day }

      @sort_by_featured = sort
    end
  end

  def redirect_correct_query_params
    make_name = params[:make]
    model_name = params[:model]
    return unless make_name.include?('+') || model_name.include?('+')

    return if MasterModel.get_by_vc_name_e(params[:model]).present? && model_name.include?('+')

    return if MasterMake.get_by_vc_name_e(params[:make]).present? && make_name.include?('+')

    params[:make] = CGI.unescape(make_name)
    params[:model] = CGI.unescape(model_name)
    if valid_params?
      match_params_data = request.url.match(/\?(.*)$/)&.[](1)
      new_make_name = CGI.escapeURIComponent(params[:make])
      new_model_name = CGI.escapeURIComponent(params[:model])
      base_url = "/used_car/#{new_make_name}/#{new_model_name}/"
      redirect_url = match_params_data ? "#{base_url}?#{match_params_data}" : base_url
      redirect_to redirect_url, status: :moved_permanently
    else
      handle_status_404
    end
  end

  def valid_params?
    Search::ValidateParamsService.valid_params(params, car_counter_instance)
  end

  def handle_status_404
    @status_404 = 404
    raise ActiveRecord::RecordNotFound
  end

  def prepare_non_query_data_internal_links
    if make_valid?
      @internal_links = Search::InternalLinks.new(@car_make, @car_model, user_country_code, smart_phone?).call

      if model_valid?
        @model_list = same_model_service(@car_make, @car_model).similar_model_list
        @popular_models_list = popular_models_service.list
      else
        @recommend_make_model = recommend_make_model_service.call
        @recommend_body_style_list = recommend_make_model_service.list_body_type
      end
    elsif all_make?
      @internal_links_all_make = Search::InternalLinksAllMake.new(user_country_code).call
    end
  end

  def prepare_data_internal_links
    if !contain_additional_search_params?
      prepare_non_query_data_internal_links
    elsif only_price_range_params?
      price_params = params.permit(:prcf, :prct)
      @internal_link_price_range = Search::InternalLinkPriceRange.new(price_params).call
    elsif (category_data = find_category_data)
      @internal_link_category = Search::InternalLinkCategory.new(category_data[:param_name], category_data[:param_value]).call
    elsif !contain_additional_search_params?('bsty')
      @internal_link_body_type = Search::InternalLinkBodyType.new(user_country_code, params[:bsty],
                                                                  current_user['userId']).call
    end
  end
end
