- not_pr_car = @cars.find { |c| c['StockPR'].nil? }

- if not_pr_car
  - content_for :serp_thumbnail do
    = render 'shared/pagemap_thumbnail', thumb_url: car_img_url(not_pr_car, width: 120, height: 120, type: 'crop')

- if @valid_page_param
  .vehicle__list-area
    .list__cars
      - show_affiliate_banner = affiliate_banner?
      - stock_pr_count = @cars.count { |car| car['StockPR'].present? } if show_affiliate_banner
      - @cars.each.with_index do |car, index|
        / Ensure that the image above the fold is not lazy-loaded to improve Lighthouse performance
        = render 'sp/search/cars/car_item', car: car, no_lazy: index < 1, include_index: true, car_counter: index
        - if show_affiliate_banner && display_affiliate_banner?((index + 1), stock_pr_count)
          = render 'shared/affiliate_banner', platform: :sp
  - if enable_paginate?
    - left = params[:pn].to_i > 2 ? -1 : 5
    .pagination-block
      = paginate @paginatable_array, window: 2, left: left, right: -1
  - elsif @car_count.positive? || @cars_pr_count.positive?
    .pagination-block
      nav.pagination
        span.page class="#{params[:pn].to_i.nonzero? ? '' : 'current'}" 1

.scroll_top__btn.text-center data-search-target='spScrollTopBtn' data-action='click->search#scrollToTop'
  span Top of Page
