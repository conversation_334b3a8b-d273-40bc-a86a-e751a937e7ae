module Search
  class InternalLinkPriceRange
    JAPAN_MAKER = [
      { maker_id: 1, maker_name: 'Toyota' },
      { maker_id: 2, maker_name: 'Nissan' },
      { maker_id: 3, maker_name: 'Honda' },
      { maker_id: 5, maker_name: 'Mazda' },
      { maker_id: 4, maker_name: 'Mitsubishi' },
    ].freeze

    SECTIONS = {
      japan_maker: :handle_japan_maker,
      body_style: :handle_body_style,
      option: :handle_option,
      popular_model: :handle_popular_model
    }.freeze

    def initialize(price_params)
      @price_range_query = price_params.to_query
      @solr_facet = Solr::FacetService.new
      @option = MasterInfo::InternalLink::OtherCategory.all
      @body_style = MasterInfo::InternalLink::BodyStyle.all
      @popular_for_all_make_service = Search::GetPopularForAllMakeService.new
    end

    def call
      return unless price_range

      Rails.cache.fetch("internal_links_price_range_#{@price_range_query}", expires_in: 1.hour) do
        SECTIONS.filter_map do |_, handler|
          link_info = send(handler)
          next if link_info.blank? || link_info[:condition].blank?

          link_info
        end
      end
    end

    private

    def price_range
      @price_range ||= MasterInfo::InternalLink::PriceRange.find_by(query: @price_range_query)
    end

    def handle_japan_maker
      condition = build_condition_data(JAPAN_MAKER, 'maker') do |make|
        ["used_car/#{make[:maker_name].downcase}/all/?#{@price_range_query}", "#{make[:maker_name]} x #{price_range[:text]}"]
      end
      { title: "Make x #{price_range[:text]}", condition: condition }
    end

    def handle_body_style
      condition = build_condition_data(@body_style, 'body_style') do |body_style|
        ["used_car/all/all/?#{@price_range_query}&#{body_style[:query]}", "#{body_style[:text]} x #{price_range[:text]}"]
      end
      { title: "BodyStyles x #{price_range[:text]}", condition: condition }
    end

    def handle_option
      condition = build_condition_data(@option, 'option') do |option|
        ["used_car/all/all/?#{@price_range_query}&#{option[:query]}", "#{price_range[:text]} x #{option[:text]}"]
      end
      { title: "#{price_range[:text]} x Option", condition: condition }
    end

    def handle_popular_model
      return if popular_models.blank?

      condition = build_condition_data(popular_models, 'model') do |model|
        ["used_car/#{model.maker_nm.downcase}/#{model.model_nm.downcase}/?#{@price_range_query}", "#{model.maker_nm} #{model.model_nm}"]
      end
      { title: 'Popular Models', condition: condition }
    end

    def build_condition_data(collection, condition_type, &block)
      collection.each_with_index.filter_map do |item, index|
        count = fetch_condition_counts["#{condition_type}_#{index}"] || 0
        next if count.zero?

        [block.call(item), count]
      end
    end

    def fetch_condition_counts
      @all_facet_counts ||= begin
        queries = combined_facet_queries
        result = @solr_facet.call_facet(queries: queries.values).dig('facet_counts', 'facet_queries') || {}
        queries.keys.zip(result.values).to_h
      end
    end

    def combined_facet_queries
      queries = []

      JAPAN_MAKER.each_with_index do |maker, index|
        queries << ["maker_#{index}", "MakeID:#{maker[:maker_id]} AND Price:#{price_range[:value]}"]
      end

      @body_style.each_with_index do |body_style, index|
        queries << ["body_style_#{index}", "BodyStyle1:#{body_style[:id]} AND Price:#{price_range[:value]}"]
      end

      @option.each_with_index do |option, index|
        queries << ["option_#{index}", "#{option[:solr_query]} AND Price:#{price_range[:value]}"]
      end

      popular_models.each_with_index do |model, index|
        queries << ["model_#{index}", "MakeID:#{model[:maker_id]} AND ModelID:#{model[:model_id]} AND Price:#{price_range[:value]}"]
      end

      queries.to_h
    end

    def popular_models
      @popular_models ||= @popular_for_all_make_service.list_all_model_in_price_range(price_range[:master_info_id])
    end
  end
end
